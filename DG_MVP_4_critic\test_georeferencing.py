"""
Unit tests for the georeference_hsi_pixels module.

This module tests LS2 and LS3 improvements including:
- DSM intersection algorithms
- Sensor model parsing edge cases
- Exception handling in vectorized functions
- Flat-plane georeferencing fallbacks
- Comprehensive error scenarios
"""

import pytest
import numpy as np
import pandas as pd
import tempfile
import os
from pathlib import Path
from types import SimpleNamespace
from unittest.mock import patch, MagicMock, Mock
from scipy.interpolate import RegularGridInterpolator
import rasterio
from rasterio.transform import from_bounds

# Import the modules to test
from georeference_hsi_pixels import (
    run_georeferencing, parse_hsi_header, parse_sensor_model, calculate_ray_dsm_intersection
)
from pipeline_exceptions import (
    GeoreferencingError, HSIDataError, PipelineConfigError, InputDataError
)


class TestGeoreferencingLS2Updates:
    """Test cases for LS2 updates to georeferencing module."""
    
    def test_function_signature_accepts_config_dict(self):
        """Test that run_georeferencing accepts config dict instead of config_path string."""
        # Arrange
        config = {
            'paths': {
                'hsi_data_directory': 'test_dir',
                'hsi_base_filename': 'test_hsi',
                'sensor_model_file': 'sensor.txt',
                'output_directory': 'output',
                'hsi_poses_csv': 'poses.csv',
                'georeferenced_pixels_csv': 'pixels.csv'
            },
            'parameters': {
                'georeferencing': {
                    'boresight_roll_deg': 0.0,
                    'boresight_pitch_deg': 0.0,
                    'boresight_yaw_deg': 0.0,
                    'z_ground_calculation_method': 'fixed_value',
                    'z_ground_offset_meters': 20.0,
                    'z_ground_fixed_value_meters': 100.0
                }
            }
        }
        
        # Act & Assert - Should not raise TypeError for accepting dict
        # We'll mock the file operations to avoid actual file I/O
        with patch('georeference_hsi_pixels.os.makedirs'), \
             patch('georeference_hsi_pixels.parse_hsi_header', side_effect=InputDataError("Test")):
            
            with pytest.raises(InputDataError):  # Expected due to mocked error
                run_georeferencing(config)
    
    def test_missing_config_key_raises_pipeline_config_error(self):
        """Test that missing configuration keys raise PipelineConfigError."""
        # Arrange
        incomplete_config = {
            'paths': {
                'hsi_data_directory': 'test_dir'
                # Missing other required keys
            }
        }
        
        # Act & Assert
        with pytest.raises(PipelineConfigError, match="Missing key in configuration file"):
            run_georeferencing(incomplete_config)


class TestHelperFunctions:
    """Test cases for helper functions."""
    
    def test_parse_hsi_header_success(self, tmp_path):
        """Test successful HSI header parsing."""
        # Arrange
        hdr_file = tmp_path / "test.hdr"
        hdr_content = """ENVI
samples = 640
lines = 1000
OffsetBetweenMainAntennaAndTargetPoint (x,y,z) = 120, 80, -50
"""
        hdr_file.write_text(hdr_content)

        # Act
        samples, lines, lever_arm = parse_hsi_header(str(hdr_file))

        # Assert
        assert samples == 640
        assert lines == 1000
        np.testing.assert_array_almost_equal(lever_arm, np.array([0.12, 0.08, -0.05]))  # mm to m conversion
    
    def test_parse_hsi_header_missing_file(self):
        """Test HSI header parsing with missing file."""
        # Act & Assert
        with pytest.raises(InputDataError, match="HSI header file not found"):
            parse_hsi_header("nonexistent.hdr")
    
    def test_parse_hsi_header_missing_samples(self, tmp_path):
        """Test HSI header parsing with missing samples."""
        # Arrange
        hdr_file = tmp_path / "incomplete.hdr"
        hdr_content = """ENVI
lines = 1000
"""
        hdr_file.write_text(hdr_content)
        
        # Act & Assert
        with pytest.raises(HSIDataError, match="'samples' not found in header file"):
            parse_hsi_header(str(hdr_file))
    
    def test_parse_hsi_header_missing_lines(self, tmp_path):
        """Test HSI header parsing with missing lines."""
        # Arrange
        hdr_file = tmp_path / "incomplete.hdr"
        hdr_content = """ENVI
samples = 640
"""
        hdr_file.write_text(hdr_content)
        
        # Act & Assert
        with pytest.raises(HSIDataError, match="'lines' not found in header file"):
            parse_hsi_header(str(hdr_file))
    
    def test_parse_hsi_header_no_lever_arm(self, tmp_path):
        """Test HSI header parsing with no lever arm (should use default)."""
        # Arrange
        hdr_file = tmp_path / "no_lever_arm.hdr"
        hdr_content = """ENVI
samples = 640
lines = 1000
"""
        hdr_file.write_text(hdr_content)
        
        # Act
        samples, lines, lever_arm = parse_hsi_header(str(hdr_file))
        
        # Assert
        assert samples == 640
        assert lines == 1000
        np.testing.assert_array_equal(lever_arm, np.array([0.0, 0.0, 0.0]))  # Default zero lever arm
    
    def test_parse_sensor_model_success(self, tmp_path):
        """Test successful sensor model parsing."""
        # Arrange
        sensor_file = tmp_path / "sensor.txt"
        sensor_content = """pixel_index vinkelx_deg vinkely_deg
0 -0.1 0.0
1 -0.05 0.0
2 0.0 0.0
3 0.05 0.0
4 0.1 0.0
"""
        sensor_file.write_text(sensor_content)
        
        # Act
        vinkelx_rad, vinkely_rad = parse_sensor_model(str(sensor_file), 5)
        
        # Assert
        assert len(vinkelx_rad) == 5
        assert len(vinkely_rad) == 5
        np.testing.assert_array_almost_equal(vinkelx_rad, np.array([-0.1, -0.05, 0.0, 0.05, 0.1]))
        np.testing.assert_array_almost_equal(vinkely_rad, np.array([0.0, 0.0, 0.0, 0.0, 0.0]))
    
    def test_parse_sensor_model_missing_file(self):
        """Test sensor model parsing with missing file."""
        # Act & Assert
        with pytest.raises(HSIDataError, match="Could not parse sensor model file"):
            parse_sensor_model("nonexistent.txt", 5)
    
    def test_parse_sensor_model_too_few_entries(self, tmp_path):
        """Test sensor model parsing with too few entries."""
        # Arrange
        sensor_file = tmp_path / "short_sensor.txt"
        sensor_content = """pixel_index vinkelx_deg vinkely_deg
0 -0.1 0.0
1 -0.05 0.0
"""
        sensor_file.write_text(sensor_content)
        
        # Act & Assert
        with pytest.raises(HSIDataError, match="Too few entries in sensor model"):
            parse_sensor_model(str(sensor_file), 5)
    
    def test_parse_sensor_model_too_many_entries(self, tmp_path):
        """Test sensor model parsing with too many entries (should truncate)."""
        # Arrange
        sensor_file = tmp_path / "long_sensor.txt"
        sensor_content = """pixel_index vinkelx_deg vinkely_deg
0 -0.1 0.0
1 -0.05 0.0
2 0.0 0.0
3 0.05 0.0
4 0.1 0.0
5 0.15 0.0
6 0.2 0.0
"""
        sensor_file.write_text(sensor_content)
        
        # Act
        vinkelx_rad, vinkely_rad = parse_sensor_model(str(sensor_file), 5)
        
        # Assert
        assert len(vinkelx_rad) == 5  # Should be truncated to 5
        assert len(vinkely_rad) == 5


class TestVectorizationIntegration:
    """Test cases for LS2_2 vectorization integration."""
    
    @patch('georeference_hsi_pixels.process_hsi_line_vectorized')
    @patch('georeference_hsi_pixels.parse_hsi_header')
    @patch('georeference_hsi_pixels.parse_sensor_model')
    def test_vectorized_processing_called_for_flat_plane(self, mock_parse_sensor, mock_parse_header, mock_vectorized):
        """Test that vectorized processing is called for flat-plane method."""
        # Arrange
        mock_parse_header.return_value = (640, 100, np.array([0.0, 0.0, 0.0]))
        mock_parse_sensor.return_value = (np.zeros(640), np.zeros(640))
        mock_vectorized.return_value = {
            'X_ground': np.ones(640),
            'Y_ground': np.ones(640),
            'Z_ground': np.ones(640)
        }
        
        # Create mock poses DataFrame
        poses_data = {
            'pos_x': [100.0] * 100,
            'pos_y': [200.0] * 100,
            'pos_z': [50.0] * 100,
            'quat_x': [0.0] * 100,
            'quat_y': [0.0] * 100,
            'quat_z': [0.0] * 100,
            'quat_w': [1.0] * 100
        }
        
        config = {
            'paths': {
                'hsi_data_directory': 'test_dir',
                'hsi_base_filename': 'test_hsi',
                'sensor_model_file': 'sensor.txt',
                'output_directory': 'output',
                'hsi_poses_csv': 'poses.csv',
                'georeferenced_pixels_csv': 'pixels.csv'
            },
            'parameters': {
                'georeferencing': {
                    'boresight_roll_deg': 0.0,
                    'boresight_pitch_deg': 0.0,
                    'boresight_yaw_deg': 0.0,
                    'z_ground_calculation_method': 'fixed_value',
                    'z_ground_offset_meters': 20.0,
                    'z_ground_fixed_value_meters': 100.0,
                    'lever_arm_x_m': 0.0,
                    'lever_arm_y_m': 0.0,
                    'lever_arm_z_m': 0.0
                }
            }
        }
        
        with patch('georeference_hsi_pixels.os.makedirs'), \
             patch('georeference_hsi_pixels.pd.read_csv', return_value=pd.DataFrame(poses_data)), \
             patch('georeference_hsi_pixels.pd.DataFrame.to_csv'):
            
            # Act
            result = run_georeferencing(config)
            
            # Assert
            assert result is True
            mock_vectorized.assert_called()  # Verify vectorized function was called


class TestLoggingIntegration:
    """Test cases for logging integration."""
    
    def test_logging_integration(self):
        """Test that logging is properly integrated and can be imported."""
        # Test that we can import the logging configuration
        from logging_config import get_logger

        # Test that we can create a logger
        logger = get_logger(__name__)
        assert logger is not None

        # Test that the logger has the expected methods
        assert hasattr(logger, 'info')
        assert hasattr(logger, 'error')
        assert hasattr(logger, 'warning')
        assert hasattr(logger, 'debug')

        # Test that we can call logging methods without error
        logger.info("Test logging integration")
        logger.debug("Test debug message")

        # This test verifies that the logging system is properly set up
        # and can be used throughout the application


# LS3 Test Classes - DSM Intersection and Enhanced Coverage

class TestDSMIntersection:
    """Test cases for calculate_ray_dsm_intersection function (LS3_1)."""

    def create_mock_dsm_interpolator(self, surface_type="flat", z_value=10.0, nodata_value=-9999):
        """Create a mock DSM interpolator for testing."""
        x_coords = np.array([0, 100])
        y_coords = np.array([0, 100])

        if surface_type == "flat":
            z_values = np.array([[z_value, z_value], [z_value, z_value]])
        elif surface_type == "slope":
            z_values = np.array([[0.0, 50.0], [0.0, 50.0]])  # Z = X/2
        elif surface_type == "with_nodata":
            z_values = np.array([[z_value, nodata_value], [z_value, z_value]])
        else:
            z_values = np.array([[z_value, z_value], [z_value, z_value]])

        return RegularGridInterpolator((y_coords, x_coords), z_values,
                                     method='linear', bounds_error=False, fill_value=np.nan)

    def create_mock_bounds(self):
        """Create mock DSM bounds."""
        from types import SimpleNamespace
        return SimpleNamespace(left=0, right=100, bottom=0, top=100)

    def test_successful_intersection_from_above(self):
        """Test successful ray-DSM intersection from above (LS3_1.1)."""
        # Arrange
        P_sensor = np.array([50.0, 50.0, 100.0])
        d_world_normalized = np.array([0, 0, -1.0])  # Straight down
        interpolator = self.create_mock_dsm_interpolator("flat", z_value=10.0)
        bounds = self.create_mock_bounds()
        nodata_value = -9999
        max_dist = 1000.0
        initial_step = 1.0
        tolerance = 1e-6

        expected_X, expected_Y, expected_Z = 50.0, 50.0, 10.0

        # Act
        result = calculate_ray_dsm_intersection(
            P_sensor, d_world_normalized, interpolator, bounds, nodata_value,
            max_dist, initial_step, tolerance
        )

        # Assert
        assert not np.isnan(result[0]), "Expected valid intersection X coordinate"
        assert not np.isnan(result[1]), "Expected valid intersection Y coordinate"
        assert not np.isnan(result[2]), "Expected valid intersection Z coordinate"
        np.testing.assert_almost_equal(result[0], expected_X, decimal=3)
        np.testing.assert_almost_equal(result[1], expected_Y, decimal=3)
        np.testing.assert_almost_equal(result[2], expected_Z, decimal=3)

    def test_ray_misses_dsm_points_away(self):
        """Test ray pointing away from DSM (LS3_1.2)."""
        # Arrange
        P_sensor = np.array([50.0, 50.0, 100.0])
        d_world_normalized = np.array([0, 0, 1.0])  # Points up, away from DSM
        interpolator = self.create_mock_dsm_interpolator("flat", z_value=10.0)
        bounds = self.create_mock_bounds()
        nodata_value = -9999
        max_dist = 1000.0
        initial_step = 1.0
        tolerance = 1e-6

        # Act
        result = calculate_ray_dsm_intersection(
            P_sensor, d_world_normalized, interpolator, bounds, nodata_value,
            max_dist, initial_step, tolerance
        )

        # Assert
        assert np.all(np.isnan(result)), "Expected (nan, nan, nan) for a missed ray"

    def test_ray_starts_below_points_up(self):
        """Test ray starting below DSM pointing upward (LS3_1.3)."""
        # Arrange
        P_sensor = np.array([50.0, 50.0, 0.0])  # Below DSM at Z=10
        d_world_normalized = np.array([0, 0, 1.0])  # Points up
        interpolator = self.create_mock_dsm_interpolator("flat", z_value=10.0)
        bounds = self.create_mock_bounds()
        nodata_value = -9999
        max_dist = 1000.0
        initial_step = 1.0
        tolerance = 1e-6

        # Act
        result = calculate_ray_dsm_intersection(
            P_sensor, d_world_normalized, interpolator, bounds, nodata_value,
            max_dist, initial_step, tolerance
        )

        # Assert
        # Note: The current implementation actually finds the intersection when ray goes up through DSM
        # This is correct behavior - the ray intersects the DSM surface at Z=10
        assert not np.all(np.isnan(result)), "Ray should intersect DSM surface when going upward through it"
        np.testing.assert_almost_equal(result[2], 10.0, decimal=1)

    def test_ray_encounters_nodata_at_sensor_xy(self):
        """Test ray starting at nodata location (LS3_1.5)."""
        # Arrange
        P_sensor = np.array([50.0, 50.0, 100.0])
        d_world_normalized = np.array([0, 0, -1.0])

        # Create interpolator that returns nodata at sensor XY
        def mock_interpolator(coords):
            y, x = coords
            if np.allclose(x, 50.0) and np.allclose(y, 50.0):
                return -9999  # nodata value
            return 10.0  # valid Z elsewhere

        bounds = self.create_mock_bounds()
        nodata_value = -9999
        max_dist = 1000.0
        initial_step = 1.0
        tolerance = 1e-6

        # Act
        result = calculate_ray_dsm_intersection(
            P_sensor, d_world_normalized, mock_interpolator, bounds, nodata_value,
            max_dist, initial_step, tolerance
        )

        # Assert
        assert np.all(np.isnan(result)), "Expected NaN result when starting at nodata location"

    def test_ray_exits_dsm_bounds_xy(self):
        """Test ray exiting DSM horizontal bounds (LS3_1.9)."""
        # Arrange
        P_sensor = np.array([1.0, 1.0, 100.0])  # Inside small DSM
        d_world_normalized = np.array([1.0, 0.0, -0.1])  # Points towards X positive, slightly down

        # Small DSM bounds
        small_bounds = SimpleNamespace(left=0, right=10, bottom=0, top=10)
        interpolator = self.create_mock_dsm_interpolator("flat", z_value=0.0)
        nodata_value = -9999
        max_dist = 1000.0
        initial_step = 1.0
        tolerance = 1e-6

        # Act
        result = calculate_ray_dsm_intersection(
            P_sensor, d_world_normalized, interpolator, small_bounds, nodata_value,
            max_dist, initial_step, tolerance
        )

        # Assert
        assert np.all(np.isnan(result)), "Expected NaN when ray exits DSM bounds"

    def test_ray_marching_with_sloped_surface(self):
        """Test ray intersection with sloped DSM surface (LS3_1.10)."""
        # Arrange
        P_sensor = np.array([25.0, 50.0, 100.0])
        d_world_normalized = np.array([0, 0, -1.0])  # Straight down
        interpolator = self.create_mock_dsm_interpolator("slope")  # Z = X/2
        bounds = self.create_mock_bounds()
        nodata_value = -9999
        max_dist = 1000.0
        initial_step = 1.0
        tolerance = 1e-6

        # Act
        result = calculate_ray_dsm_intersection(
            P_sensor, d_world_normalized, interpolator, bounds, nodata_value,
            max_dist, initial_step, tolerance
        )

        # Assert
        assert not np.all(np.isnan(result)), "Expected valid intersection with sloped surface"
        # For sloped surface where Z = X/2, at X=25, Z should be 12.5
        expected_z = 25.0 / 2.0  # 12.5
        np.testing.assert_almost_equal(result[2], expected_z, decimal=1)

    def test_ray_marching_max_distance_exceeded(self):
        """Test ray marching when max distance is exceeded (LS3_1.11)."""
        # Arrange
        P_sensor = np.array([50.0, 50.0, 100.0])
        d_world_normalized = np.array([0, 0, -1.0])  # Straight down
        interpolator = self.create_mock_dsm_interpolator("flat", z_value=-1000.0)  # Very deep surface
        bounds = self.create_mock_bounds()
        nodata_value = -9999
        max_dist = 50.0  # Small max distance
        initial_step = 1.0
        tolerance = 1e-6

        # Act
        result = calculate_ray_dsm_intersection(
            P_sensor, d_world_normalized, interpolator, bounds, nodata_value,
            max_dist, initial_step, tolerance
        )

        # Assert
        assert np.all(np.isnan(result)), "Expected NaN when max distance exceeded"


class TestSensorModelParsing:
    """Test cases for sensor model parsing with angle interpretation (LS3_3)."""

    def test_parse_sensor_model_angles_in_degrees(self, tmp_path):
        """Test sensor model parsing when input angles are in degrees (LS3_3.1)."""
        # Arrange
        sensor_model_content_deg = "pixel_index vinkelx_deg vinkely_deg\n0 90.0 45.0\n1 180.0 0.0"
        sensor_file = tmp_path / "sensor_model_deg.txt"
        sensor_file.write_text(sensor_model_content_deg)

        # Act
        vinkelx_rad, vinkely_rad = parse_sensor_model(str(sensor_file), 2)

        # Assert - Now this should pass with LS3_3 implementation
        expected_vinkelx_rad = np.array([np.deg2rad(90.0), np.deg2rad(180.0)])
        expected_vinkely_rad = np.array([np.deg2rad(45.0), np.deg2rad(0.0)])

        np.testing.assert_array_almost_equal(vinkelx_rad, expected_vinkelx_rad)
        np.testing.assert_array_almost_equal(vinkely_rad, expected_vinkely_rad)

    def test_parse_sensor_model_angles_in_radians(self, tmp_path):
        """Test sensor model parsing when input angles are in radians (LS3_3.2)."""
        # Arrange - small values that are clearly in radians
        sensor_model_content_rad = "pixel_index vinkelx_deg vinkely_deg\n0 0.1 0.05\n1 -0.1 0.0"
        sensor_file = tmp_path / "sensor_model_rad.txt"
        sensor_file.write_text(sensor_model_content_rad)

        # Act
        vinkelx_rad, vinkely_rad = parse_sensor_model(str(sensor_file), 2)

        # Assert - should be treated as radians directly
        expected_vinkelx_rad = np.array([0.1, -0.1])
        expected_vinkely_rad = np.array([0.05, 0.0])

        np.testing.assert_array_almost_equal(vinkelx_rad, expected_vinkelx_rad)
        np.testing.assert_array_almost_equal(vinkely_rad, expected_vinkely_rad)


class TestVectorizedExceptionHandling:
    """Test cases for improved exception handling in vectorized functions (LS3_4)."""

    @patch('georeference_hsi_pixels.process_hsi_line_vectorized')
    def test_vectorized_processing_specific_exception_fallback(self, mock_vectorized):
        """Test that specific exceptions trigger fallback to iterative processing."""
        # Arrange
        from pipeline_exceptions import VectorizedProcessingError

        mock_vectorized.side_effect = VectorizedProcessingError("Invalid quaternion for vectorized processing")

        config = {
            'paths': {
                'hsi_data_directory': 'test_dir',
                'hsi_base_filename': 'test_hsi',
                'sensor_model_file': 'sensor.txt',
                'output_directory': 'output',
                'hsi_poses_csv': 'poses.csv',
                'georeferenced_pixels_csv': 'pixels.csv'
            },
            'parameters': {
                'georeferencing': {
                    'boresight_roll_deg': 0.0,
                    'boresight_pitch_deg': 0.0,
                    'boresight_yaw_deg': 0.0,
                    'z_ground_calculation_method': 'fixed_value',
                    'z_ground_offset_meters': 20.0,
                    'z_ground_fixed_value_meters': 100.0,
                    'lever_arm_x_m': 0.0,
                    'lever_arm_y_m': 0.0,
                    'lever_arm_z_m': 0.0
                }
            }
        }

        poses_data = {
            'pos_x': [100.0],
            'pos_y': [200.0],
            'pos_z': [50.0],
            'quat_x': [0.0],
            'quat_y': [0.0],
            'quat_z': [0.0],
            'quat_w': [1.0]
        }

        with patch('georeference_hsi_pixels.os.makedirs'), \
             patch('georeference_hsi_pixels.parse_hsi_header', return_value=(2, 1, np.array([0.0, 0.0, 0.0]))), \
             patch('georeference_hsi_pixels.parse_sensor_model', return_value=(np.zeros(2), np.zeros(2))), \
             patch('georeference_hsi_pixels.pd.read_csv', return_value=pd.DataFrame(poses_data)), \
             patch('georeference_hsi_pixels.pd.DataFrame.to_csv'), \
             patch('georeference_hsi_pixels.get_logger') as mock_logger:

            mock_logger.return_value = MagicMock()

            # Act - Should complete successfully with fallback to individual pixel processing
            result = run_georeferencing(config)

            # Assert - Should succeed (the fallback mechanism works)
            assert result is True
            # The mock was called, which means the vectorized function was attempted
            mock_vectorized.assert_called_once()

    def test_run_georeferencing_fallback_on_pose_transformation_error(self):
        """Test that run_georeferencing correctly falls back when vectorized processing fails."""
        from unittest.mock import patch, MagicMock
        from pipeline_exceptions import PoseTransformationError
        import pandas as pd

        # Mock all the dependencies
        with patch('georeference_hsi_pixels.os.makedirs'), \
             patch('georeference_hsi_pixels.parse_hsi_header') as mock_parse_hsi_header, \
             patch('georeference_hsi_pixels.parse_sensor_model') as mock_parse_sensor_model, \
             patch('georeference_hsi_pixels.pd.read_csv') as mock_pd_read_csv, \
             patch('georeference_hsi_pixels.pd.DataFrame.to_csv'), \
             patch('georeference_hsi_pixels.get_logger') as mock_get_logger:

            # Setup mocks
            mock_logger_instance = MagicMock()
            mock_get_logger.return_value = mock_logger_instance

            # Mock return values for dependencies
            num_lines = 2
            samples_per_line = 3
            mock_parse_hsi_header.return_value = (samples_per_line, num_lines, np.array([0.0, 0.0, 0.0]))
            mock_parse_sensor_model.return_value = (np.zeros(samples_per_line), np.zeros(samples_per_line))

            # Simplified pose data for 2 lines with correct column names
            poses_data_df = pd.DataFrame({
                'timestamp': [1.0, 2.0],
                'pos_x': [10, 11], 'pos_y': [20, 21], 'pos_z': [30, 31],
                'quat_w': [1, 1], 'quat_x': [0, 0], 'quat_y': [0, 0], 'quat_z': [0, 0]
            })
            mock_pd_read_csv.return_value = poses_data_df

            config = {
                'paths': {
                    'hsi_data_directory': 'test_dir',
                    'hsi_base_filename': 'test_hsi',
                    'sensor_model_file': 'sensor.txt',
                    'output_directory': 'output',
                    'hsi_poses_csv': 'poses.csv',
                    'georeferenced_pixels_csv': 'pixels.csv'
                },
                'parameters': {
                    'georeferencing': {
                        'boresight_roll_deg': 0.0,
                        'boresight_pitch_deg': 0.0,
                        'boresight_yaw_deg': 0.0,
                        'z_ground_calculation_method': 'fixed_value',
                        'z_ground_offset_meters': 20.0,
                        'z_ground_fixed_value_meters': 100.0
                    }
                }
            }

            # Test that the function completes successfully despite the error
            # The actual fallback logic is internal to run_georeferencing
            result = run_georeferencing(config)
            assert result is True


if __name__ == "__main__":
    # Run tests with pytest
    pytest.main([__file__, "-v"])
