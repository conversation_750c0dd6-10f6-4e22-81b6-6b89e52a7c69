<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_718ce007.css" type="text/css">
    <script src="coverage_html_cb_497bf287.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">36%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button current">Files</a>
                <a class="button" href="function_index.html">Functions</a>
                <a class="button" href="class_index.html">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.8.2">coverage.py v7.8.2</a>,
            created at 2025-06-02 16:33 +0200
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="compare_timestamps_py.html">compare_timestamps.py</a></td>
                <td>56</td>
                <td>56</td>
                <td>2</td>
                <td class="right" data-ratio="0 56">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="create_consolidated_webodm_poses_py.html">create_consolidated_webodm_poses.py</a></td>
                <td>112</td>
                <td>29</td>
                <td>17</td>
                <td class="right" data-ratio="83 112">74%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="create_georeferenced_rgb_py.html">create_georeferenced_rgb.py</a></td>
                <td>178</td>
                <td>165</td>
                <td>12</td>
                <td class="right" data-ratio="13 178">7%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="georeference_hsi_pixels_py.html">georeference_hsi_pixels.py</a></td>
                <td>390</td>
                <td>377</td>
                <td>8</td>
                <td class="right" data-ratio="13 390">3%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="lever_arm_utils_py.html">lever_arm_utils.py</a></td>
                <td>39</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="38 39">97%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="logging_config_py.html">logging_config.py</a></td>
                <td>14</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="14 14">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="main_pipeline_py.html">main_pipeline.py</a></td>
                <td>84</td>
                <td>14</td>
                <td>6</td>
                <td class="right" data-ratio="70 84">83%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="pipeline_exceptions_py.html">pipeline_exceptions.py</a></td>
                <td>16</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="16 16">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="plot_hsi_data_py.html">plot_hsi_data.py</a></td>
                <td>118</td>
                <td>107</td>
                <td>8</td>
                <td class="right" data-ratio="11 118">9%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="synchronize_hsi_webodm_py.html">synchronize_hsi_webodm.py</a></td>
                <td>232</td>
                <td>82</td>
                <td>17</td>
                <td class="right" data-ratio="150 232">65%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="vectorized_georef_py.html">vectorized_georef.py</a></td>
                <td>63</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="56 63">89%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td>1302</td>
                <td>838</td>
                <td>70</td>
                <td class="right" data-ratio="464 1302">36%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.8.2">coverage.py v7.8.2</a>,
            created at 2025-06-02 16:33 +0200
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href="vectorized_georef_py.html"></a>
        <a id="nextFileLink" class="nav" href="compare_timestamps_py.html"></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>
