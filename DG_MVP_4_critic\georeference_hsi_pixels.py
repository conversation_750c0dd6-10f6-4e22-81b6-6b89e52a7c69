import pandas as pd
import numpy as np
from scipy.spatial.transform import Rotation
import toml
import os
import rasterio
import datetime
from scipy.interpolate import RegularGridInterpolator
from scipy.optimize import brentq

# --- Coordinate System Definitions ---
# IMU-Body Coordinate System (Assumed FRD - Forward, Right, Down):
#   X-axis: Forward (in the direction of flight of the drone)
#   Y-axis: Right (from the pilot's perspective)
#   Z-axis: Down (towards the Earth)
#
# Sensor Coordinate System (Linescanner - Assumed):
#   Z_sensor-axis: Optical axis (ideally pointing downwards for nadir shots)
#   X_sensor-axis: Along the scan line (across-track)
#   Y_sensor-axis: Along the flight direction of the sensor (along-track, perpendicular to scan line)
#
# --- Positive Rotation Definitions (Right-Hand Rule) ---
# Based on the IMU-Body Coordinate System:
#   Positive Roll: Rotation around the X-body axis.
#   Positive Pitch: Rotation around the Y-body axis.
#   Positive Yaw: Rotation around the Z-body axis.
#
# --- Boresight Angles ---
# These angles define the rotation FROM the Body system TO the Sensor system.
# R_body_to_sensor = Rz(yaw_bs) @ Ry(pitch_bs) @ Rx(roll_bs)
# The script calculates R_sensor_to_body = R_body_to_sensor.T

def parse_hsi_header(hdr_file_path):
    """
    Liest die HSI-Header-Datei und extrahiert 'samples', 'lines' und 'lever arm'.

    Args:
        hdr_file_path (str): Pfad zur .hdr-Datei.

    Returns:
        tuple: (samples, lines, lever_arm_body)
               samples (int): Anzahl der Samples pro Zeile.
               lines (int): Anzahl der Zeilen.
               lever_arm_body (np.array): Lever-Arm-Vektor [x, y, z].
    """
    header_data = {}
    with open(hdr_file_path, 'r') as f:
        for line in f:
            line = line.strip()
            if '=' in line:
                key, value = line.split('=', 1)
                key = key.strip()
                value = value.strip()
                if key == 'samples':
                    header_data['samples'] = int(value)
                elif key == 'lines':
                    header_data['lines'] = int(value)
                elif key.startswith('OffsetBetweenMainAntennaAndTargetPoint'):
                    value = value.strip().replace('(', '').replace(')', '')
                    parts = value.split(',')
                    if len(parts) == 3:
                        header_data['lever_arm'] = np.array([float(p.strip()) / 1000.0 for p in parts])
                    else:
                        raise ValueError(f"Konnte Lever-Arm-Werte nicht korrekt aus '{value}' parsen.")
                elif key == 'lever arm': 
                    value = value.replace('{', '').replace('}', '').replace(',', ' ')
                    header_data['lever_arm'] = np.array([float(v) for v in value.split()])

    if 'samples' not in header_data:
        raise ValueError("'samples' nicht in Header-Datei gefunden.")
    if 'lines' not in header_data:
        raise ValueError("'lines' nicht in Header-Datei gefunden.")
    if 'lever_arm' not in header_data:
        raise ValueError("Kein gültiger 'lever arm' oder 'OffsetBetweenMainAntennaAndTargetPoint' Eintrag in Header-Datei gefunden.")
        
    return header_data['samples'], header_data['lines'], header_data['lever_arm']

def parse_sensor_model(sensor_model_path, num_samples):
    """
    Liest das Sensor-Winkelmodell.
    Winkel werden als Radiant interpretiert.
    """
    try:
        sensor_data = pd.read_csv(sensor_model_path, delim_whitespace=True, header=None, skiprows=1,
                                  names=['pixel_index', 'vinkelx_deg', 'vinkely_deg'])
    except Exception:
        try:
            sensor_data = pd.read_csv(sensor_model_path, delim_whitespace=True, header=None,
                                      names=['pixel_index', 'vinkelx_deg', 'vinkely_deg'])
        except pd.errors.ParserError:
            try:
                sensor_data_raw = pd.read_csv(sensor_model_path, delim_whitespace=True, header=None,
                                          names=['vinkelx_deg', 'vinkely_deg'])
                sensor_data = pd.DataFrame({
                    'pixel_index': np.arange(len(sensor_data_raw)),
                    'vinkelx_deg': sensor_data_raw['vinkelx_deg'],
                    'vinkely_deg': sensor_data_raw['vinkely_deg']
                })
            except Exception as final_e:
                raise ValueError(f"Konnte Sensormodelldatei nicht parsen: {sensor_model_path}. Fehler: {final_e}")

    if len(sensor_data) != num_samples:
        print(f"Warnung: Anzahl der Einträge im Sensormodell ({len(sensor_data)}) stimmt nicht mit 'samples' ({num_samples}) überein.")
        if len(sensor_data) < num_samples:
             raise ValueError(f"Zu wenige Einträge im Sensormodell. Erwartet: {num_samples}, Gefunden: {len(sensor_data)}")
        sensor_data = sensor_data.iloc[:num_samples]

    print("  INFO: Interpreting 'vinkelx_deg' and 'vinkely_deg' columns from sensor model as RADIANS directly.")
    vinkelx_rad = sensor_data['vinkelx_deg'].values
    vinkely_rad = sensor_data['vinkely_deg'].values
    
    return vinkelx_rad, vinkely_rad

def calculate_ray_dsm_intersection(P_sensor, d_world_normalized, interpolator, bounds, nodata_value, max_dist, initial_step, tolerance):
    """
    Calculates the intersection of a 3D ray with a Digital Surface Model (DSM).
    """
    # Function to get DSM height at a point (x, y)
    def get_dsm_z(x, y):
        if not (bounds.left <= x <= bounds.right and bounds.bottom <= y <= bounds.top):
            return np.nan
        z_val = interpolator((y, x)) # Interpolator expects (y, x), returns scalar
        # Check against nodata_value only if nodata_value itself is not NaN
        if nodata_value is not None and not np.isnan(nodata_value) and np.isclose(z_val, nodata_value):
            return np.nan
        # If nodata_value is np.nan, interpolator's fill_value=np.nan handles it.
        # If z_val is already np.nan (e.g. from interpolator fill_value), it's fine.
        return z_val

    # Function to solve for t: ray_z(t) - dsm_z(ray_xy(t)) = 0
    def func_to_solve(t):
        P_intersect = P_sensor + t * d_world_normalized
        x_ray, y_ray, z_ray = P_intersect[0], P_intersect[1], P_intersect[2]
        
        z_dsm = get_dsm_z(x_ray, y_ray)
        if np.isnan(z_dsm):
            # Heuristic: return a value indicating the ray is "above" an arbitrary low plane
            # to guide brentq if it encounters NaN during its search.
            return z_ray - (P_sensor[2] - 10000) # Large difference
        return z_ray - z_dsm

    # Initial point on ray (sensor position)
    z_ray_start = P_sensor[2]
    z_dsm_start = get_dsm_z(P_sensor[0], P_sensor[1])

    t_current = 0.0 # Parameter t along the ray

    # If starting point is already outside DSM or on nodata, try to find an entry point
    if np.isnan(z_dsm_start):
        found_entry = False
        for t_entry_candidate in np.arange(initial_step, max_dist, initial_step): # Start search from initial_step
            p_candidate = P_sensor + t_entry_candidate * d_world_normalized
            z_dsm_candidate = get_dsm_z(p_candidate[0], p_candidate[1])
            if not np.isnan(z_dsm_candidate):
                # Found a valid point on DSM along the ray
                t_current = t_entry_candidate
                z_ray_start = p_candidate[2]
                z_dsm_start = z_dsm_candidate
                found_entry = True
                break
        if not found_entry:
            return np.nan, np.nan, np.nan # No valid entry point found

    # We have a valid starting point (z_ray_start, z_dsm_start) at t_current
    diff_prev = z_ray_start - z_dsm_start
    t_prev = t_current

    # Ray Marching Loop
    step = initial_step
    t_search = t_current

    while t_search <= max_dist:
        t_search += step
        P_current_ray = P_sensor + t_search * d_world_normalized
        x_ray_curr, y_ray_curr, z_ray_curr = P_current_ray[0], P_current_ray[1], P_current_ray[2]

        z_dsm_curr = get_dsm_z(x_ray_curr, y_ray_curr)

        if np.isnan(z_dsm_curr): # Ray exited DSM valid area or hit nodata
            return np.nan, np.nan, np.nan

        diff_curr = z_ray_curr - z_dsm_curr

        if diff_prev * diff_curr <= 0: # Sign change indicates intersection in [t_prev, t_search]
            try:
                # Ensure func_to_solve at interval ends are not NaN for brentq
                val_at_a = func_to_solve(t_prev)
                val_at_b = func_to_solve(t_search)

                if np.isnan(val_at_a) or np.isnan(val_at_b): # Should be rare if get_dsm_z was not nan for diff_prev/curr
                    # This implies func_to_solve's heuristic for NaN didn't prevent brentq issues
                    # Or the point became NaN exactly at the boundary for brentq's check
                    # Try to slightly adjust interval if possible, or abandon
                    if np.isnan(val_at_a) and not np.isnan(func_to_solve(t_prev + tolerance / 100)): val_at_a = func_to_solve(t_prev + tolerance/100); t_prev_brentq = t_prev + tolerance/100
                    else: t_prev_brentq = t_prev
                    if np.isnan(val_at_b) and not np.isnan(func_to_solve(t_search - tolerance/100)): val_at_b = func_to_solve(t_search - tolerance/100); t_search_brentq = t_search - tolerance/100
                    else: t_search_brentq = t_search
                    
                    if np.isnan(val_at_a) or np.isnan(val_at_b) or val_at_a * val_at_b > 0: # Still problematic
                        # Fallback: if one is nan, and the other isn't, maybe the root is at the non-nan point if diff is zero there
                        if (np.isnan(val_at_a) and np.isclose(val_at_b,0)) or (np.isnan(val_at_b) and np.isclose(val_at_a,0)):
                             # This is too specific, better to let it fail or continue marching
                             pass # Let brentq try or fail, or continue marching
                        else: # Can't form a valid bracket for brentq
                            diff_prev = diff_curr
                            t_prev = t_search
                            step = initial_step # Reset step
                            continue


                # If val_at_a * val_at_b > 0 after checks, brentq will fail.
                # This means no strict sign change for brentq with current func_to_solve behavior.
                # This can happen with grazing incidence or if the heuristic for NaN in func_to_solve isn't perfect.
                if val_at_a * val_at_b > 0:
                    # If one of them is very close to zero, we might be at the intersection
                    if np.isclose(val_at_a, 0, atol=tolerance):
                        t_intersect = t_prev
                        P_ground = P_sensor + t_intersect * d_world_normalized
                        return P_ground[0], P_ground[1], P_ground[2]
                    if np.isclose(val_at_b, 0, atol=tolerance):
                        t_intersect = t_search
                        P_ground = P_sensor + t_intersect * d_world_normalized
                        return P_ground[0], P_ground[1], P_ground[2]
                    
                    # Otherwise, no clear intersection in this step for brentq
                    diff_prev = diff_curr
                    t_prev = t_search
                    # Optional: reduce step size if grazing suspected
                    # step = max(tolerance, step / 2)
                    continue

                t_intersect = brentq(func_to_solve, t_prev, t_search, xtol=tolerance, rtol=tolerance)
                P_ground = P_sensor + t_intersect * d_world_normalized
                return P_ground[0], P_ground[1], P_ground[2]
            except ValueError: # brentq failed (e.g. signs not opposite, or NaN)
                # Continue ray marching. The interval might not have been good.
                pass # Fall through to update diff_prev and t_prev
            except Exception: # Other unexpected error
                return np.nan, np.nan, np.nan # Give up on this ray

        diff_prev = diff_curr
        t_prev = t_search
        # Adaptive step could be added here based on diff_curr
        # e.g., if abs(diff_curr) is small, reduce step size

    return np.nan, np.nan, np.nan # No intersection found within max_dist

def run_georeferencing(config: dict) -> bool:
    """
    Execute HSI pixel georeferencing based on the provided configuration.

    Args:
        config: Configuration dictionary loaded from TOML file

    Returns:
        True if georeferencing completed successfully, False otherwise

    Raises:
        GeoreferencingError: If georeferencing process fails
        HSIDataError: If HSI data cannot be processed
        PipelineConfigError: If configuration is invalid
    """
    from logging_config import get_logger
    from pipeline_exceptions import GeoreferencingError, HSIDataError, PipelineConfigError
    from lever_arm_utils import determine_effective_lever_arm, validate_lever_arm

    logger = get_logger(__name__)
    logger.info("Starting HSI pixel georeferencing")

    logger.info("Reading path configuration...")
    try:
        hsi_data_dir = config['paths']['hsi_data_directory']
        hsi_base_filename = config['paths']['hsi_base_filename']
        hdr_file_path = os.path.join(hsi_data_dir, f"{hsi_base_filename}.hdr")

        sensor_model_filename = config['paths']['sensor_model_file']
        sensor_model_path = os.path.join(hsi_data_dir, sensor_model_filename)

        output_dir = config['paths']['output_directory']
        os.makedirs(output_dir, exist_ok=True)
        logger.info(f"Output directory: {output_dir} (created if not exists)")

        hsi_poses_input_filename = config['paths']['hsi_poses_csv']
        poses_file_path = os.path.join(output_dir, hsi_poses_input_filename)
        logger.info(f"HSI poses CSV (expected in output_dir): {poses_file_path}")

        base_output_filename = config['paths']['georeferenced_pixels_csv']
        output_file_path = os.path.join(output_dir, base_output_filename)
        logger.info(f"Output georeferenced pixels CSV: {output_file_path}")

    except KeyError as e:
        raise PipelineConfigError(f"Missing key in configuration file [paths]: {e}") from e

    logger.info("Reading georeferencing parameters...")
    try:
        boresight_roll_deg = config['parameters']['georeferencing']['boresight_roll_deg']
        boresight_pitch_deg = config['parameters']['georeferencing']['boresight_pitch_deg']
        boresight_yaw_deg = config['parameters']['georeferencing']['boresight_yaw_deg']
        logger.info(f"Boresight angles (degrees): Roll={boresight_roll_deg}, Pitch={boresight_pitch_deg}, Yaw={boresight_yaw_deg}")

        z_ground_method = config['parameters']['georeferencing']['z_ground_calculation_method']
        z_ground_offset = config['parameters']['georeferencing']['z_ground_offset_meters']
        z_ground_fixed = config['parameters']['georeferencing']['z_ground_fixed_value_meters']

        dsm_interpolator = None
        dsm_bounds = None
        dsm_nodata_value = np.nan  # Default to np.nan if not specified or None in GeoTIFF
        ray_max_dist = config['parameters']['georeferencing'].get('ray_dsm_max_search_dist_m', 2000.0)
        ray_initial_step = config['parameters']['georeferencing'].get('ray_dsm_step_m', 5.0)
        ray_bisection_tol = config['parameters']['georeferencing'].get('ray_dsm_bisection_tolerance_m', 0.01)

        # Sensor model correction parameters
        scale_vinkel_x = config['parameters']['georeferencing'].get('scale_vinkel_x', 1.0)
        offset_vinkel_x = config['parameters']['georeferencing'].get('offset_vinkel_x', 0.0)
        logger.info(f"Sensor model correction: scale_vinkel_x={scale_vinkel_x}, offset_vinkel_x={offset_vinkel_x}")

        # Lever arm parameters from config (will be processed after reading HDR)
        config_lever_arm_x = config['parameters']['georeferencing'].get('lever_arm_x_m', 0.0)
        config_lever_arm_y = config['parameters']['georeferencing'].get('lever_arm_y_m', 0.0)
        config_lever_arm_z = config['parameters']['georeferencing'].get('lever_arm_z_m', 0.0)
        config_lever_arm = np.array([config_lever_arm_x, config_lever_arm_y, config_lever_arm_z])

        logger.info(f"Z_ground method: {z_ground_method}, Offset: {z_ground_offset}, Fixed value: {z_ground_fixed}")
        if z_ground_method == "dsm_intersection":
            logger.info(f"Ray marching params: MaxDist={ray_max_dist}m, Step={ray_initial_step}m, Tol={ray_bisection_tol}m")

    except KeyError as e:
        raise PipelineConfigError(f"Missing key in configuration file [parameters.georeferencing]: {e}") from e

    if z_ground_method == "dsm_intersection":
        print("Lade DSM für 'dsm_intersection' Methode...")
        try:
            dsm_file_path_from_config = config['paths']['dsm_file']
            # Assuming dsm_file_path_from_config is relative to project root, or absolute
            dsm_path = dsm_file_path_from_config
            if not os.path.isabs(dsm_path):
                 dsm_path = os.path.join(os.getcwd(), dsm_path) 

            print(f"  Versuche DSM zu öffnen: {dsm_path}")
            with rasterio.open(dsm_path) as src:
                dsm_array = src.read(1).astype(np.float32)
                dsm_transform = src.transform
                # Use src.nodatavals[0] if available and not None, otherwise keep dsm_nodata_value as np.nan
                if src.nodatavals and src.nodatavals[0] is not None:
                    dsm_nodata_value = float(src.nodatavals[0]) 
                dsm_bounds = src.bounds 
                print(f"  DSM erfolgreich geladen. Shape: {dsm_array.shape}, NoData: {dsm_nodata_value}")
                print(f"  DSM Transform: {dsm_transform}")
                print(f"  DSM Bounds: {dsm_bounds}")

            cols = np.arange(dsm_array.shape[1])
            rows = np.arange(dsm_array.shape[0])
            
            x_coords_dsm = np.array([dsm_transform.c + dsm_transform.a * (i + 0.5) for i in range(dsm_array.shape[1])])
            y_coords_dsm_raw = np.array([dsm_transform.f + dsm_transform.e * (i + 0.5) for i in range(dsm_array.shape[0])])

            dsm_array_for_interp = np.copy(dsm_array) # Work on a copy

            if dsm_transform.e < 0: 
                y_coords_dsm_asc = y_coords_dsm_raw[::-1]
                dsm_array_for_interp = dsm_array_for_interp[::-1, :] 
            else:
                y_coords_dsm_asc = y_coords_dsm_raw

            if not np.isnan(dsm_nodata_value): # Only replace if nodata_value is a specific number
                nodata_mask = np.isclose(dsm_array_for_interp, dsm_nodata_value)
                dsm_array_for_interp[nodata_mask] = np.nan
            
            # Ensure all actual NaNs (either original or converted nodata) are handled by fill_value
            # This is implicitly handled by fill_value=np.nan in RegularGridInterpolator

            dsm_interpolator = RegularGridInterpolator(
                (y_coords_dsm_asc, x_coords_dsm), dsm_array_for_interp,
                method='linear', bounds_error=False, fill_value=np.nan # Changed 'bilinear' to 'linear'
            )
            print("  DSM Interpolator (RegularGridInterpolator) erfolgreich initialisiert.")

        except FileNotFoundError:
            print(f"FEHLER: DSM-Datei nicht gefunden unter: {dsm_path}")
            return False
        except rasterio.errors.RasterioIOError as e:
            print(f"FEHLER: Rasterio konnte DSM-Datei nicht öffnen oder lesen: {dsm_path}. Fehler: {e}")
            return False
        except KeyError as e:
            print(f"FEHLER: Fehlender Schlüssel für DSM-Pfad in der Konfigurationsdatei: {e}")
            return False
        except Exception as e:
            print(f"FEHLER: Ein unerwarteter Fehler ist beim Laden oder Verarbeiten des DSM aufgetreten: {e}")
            return False

    logger.info(f"Reading HSI header file: {hdr_file_path}")
    try:
        num_samples_hdr, num_lines_hdr, lever_arm_from_hdr = parse_hsi_header(hdr_file_path)
    except ValueError as e:
        raise HSIDataError(f"Error parsing HSI header file: {e}") from e

    logger.info(f"Header info: Samples: {num_samples_hdr}, Lines: {num_lines_hdr}, Lever arm from HDR: {lever_arm_from_hdr}")

    # Determine effective lever arm using the new logic
    effective_lever_arm_body, config_override_used = determine_effective_lever_arm(
        lever_arm_from_hdr, config_lever_arm
    )

    # Validate the effective lever arm
    if not validate_lever_arm(effective_lever_arm_body):
        raise GeoreferencingError("Invalid effective lever arm determined")

    logger.info(f"Effective lever arm (m) to be used in calculations: {effective_lever_arm_body}")
    if config_override_used:
        logger.info("Using lever arm from configuration override")
    else:
        logger.info("Using lever arm from HSI header")

    # Use num_samples and num_lines from header for consistency
    num_samples = num_samples_hdr
    num_lines = num_lines_hdr

    print(f"Lese Sensor-Winkelmodell: {sensor_model_path}")
    try:
        vinkelx_rad_all_pixels, vinkely_rad_all_pixels = parse_sensor_model(sensor_model_path, num_samples)
    except ValueError as e:
        print(f"FEHLER beim Parsen der Sensor-Modelldatei: {e}")
        return False
    print(f"  Sensorwinkel für {len(vinkelx_rad_all_pixels)} Pixel geladen.")
    print(f"  Original vinkelx_rad (now treated as rad) range: min={np.min(vinkelx_rad_all_pixels):.3f} rad, max={np.max(vinkelx_rad_all_pixels):.3f} rad")
    print(f"  Equivalent degrees: min={np.rad2deg(np.min(vinkelx_rad_all_pixels)):.2f} deg, max={np.rad2deg(np.max(vinkelx_rad_all_pixels)):.2f} deg")

    print(f"Lese Posen-Datei: {poses_file_path}")
    try:
        poses_df = pd.read_csv(poses_file_path)
    except FileNotFoundError:
        print(f"FEHLER: Posen-Datei nicht gefunden: {poses_file_path}")
        return False
    except Exception as e:
        print(f"FEHLER: Posen-Datei konnte nicht gelesen werden {poses_file_path}: {e}")
        return False
    print(f"  {len(poses_df)} Posen geladen.")

    if len(poses_df) != num_lines:
        print(f"FEHLER: Anzahl der Posen ({len(poses_df)}) stimmt nicht mit der Anzahl der HSI-Zeilen ({num_lines}) überein.")
        return False
    
    # --- Berechnung des mittleren Yaw-Winkels (optional, für Info) ---
    # (Code für mittleren Yaw kann hier bleiben, falls gewünscht, oder entfernt werden, wenn nicht benötigt)

    # Z_ground-Berechnung für nicht-DSM Methoden
    Z_ground_flat_plane = np.nan 
    default_fallback_z_ground = z_ground_fixed 

    if 'pos_z' in poses_df.columns and poses_df['pos_z'].notna().any():
        avg_pose_z_val = poses_df['pos_z'].mean()
        default_fallback_z_ground = avg_pose_z_val - 20.0 
    else:
        print("WARNUNG: Spalte 'pos_z' nicht in Posen-DataFrame gefunden oder enthält nur NaNs.")
        if z_ground_method == "avg_pose_z_minus_offset":
            print(f"FEHLER: z_ground_method ist '{z_ground_method}', aber 'pos_z' fehlt. Kann Z_ground nicht berechnen.")
            return False

    if z_ground_method == "avg_pose_z_minus_offset":
        if 'pos_z' in poses_df.columns and poses_df['pos_z'].notna().any():
            avg_pose_z = poses_df['pos_z'].mean()
            Z_ground_flat_plane = avg_pose_z - z_ground_offset
            print(f"  Z_ground (flat plane) Methode: 'avg_pose_z_minus_offset'. Avg Z: {avg_pose_z:.3f} m, Offset: {z_ground_offset} m")
        else: # Should have been caught
            Z_ground_flat_plane = default_fallback_z_ground
    elif z_ground_method == "fixed_value":
        Z_ground_flat_plane = z_ground_fixed
        print(f"  Z_ground (flat plane) Methode: 'fixed_value'.")
    elif z_ground_method == "dsm_intersection":
        if dsm_interpolator is None:
            print("FEHLER: z_ground_method ist 'dsm_intersection', aber der DSM-Interpolator wurde nicht initialisiert.")
            return False
        print(f"  Z_ground Methode: 'dsm_intersection'. Z_ground wird pro Strahl aus DSM berechnet.")
    else: 
        print(f"Unbekannte z_ground_calculation_method: '{z_ground_method}'. Verwende Fallback für flache Ebene.")
        Z_ground_flat_plane = default_fallback_z_ground
        print(f"  Z_ground (flat plane) Methode: Fallback. Wert: {Z_ground_flat_plane:.2f} m")

    if z_ground_method != "dsm_intersection":
        if np.isnan(Z_ground_flat_plane):
            print("FEHLER: Z_ground_flat_plane konnte nicht bestimmt werden. Skript wird beendet.")
            return False
        print(f"Verwendete Z_ground_flat_plane für Projektion (nicht-DSM Methoden): {Z_ground_flat_plane:.2f} Meter")
    
    boresight_deg_array_for_body_to_sensor = np.array([-boresight_yaw_deg, -boresight_roll_deg, boresight_pitch_deg])
    R_body_to_sensor = Rotation.from_euler('zyx', boresight_deg_array_for_body_to_sensor, degrees=True).as_matrix()
    R_sensor_to_body = R_body_to_sensor.T

    print(f"  Boresight Winkel (Grad) verwendet: Roll={boresight_roll_deg}, Pitch={boresight_pitch_deg}, Yaw={boresight_yaw_deg}")
    print(f"  R_sensor_to_body (Sensor -> Body):\n{R_sensor_to_body}")
    
    debug_lines = {0, num_lines // 2, num_lines - 1}
    debug_pixels = {0, num_samples // 2, num_samples - 1}
    print(f"Debugging output enabled for lines: {debug_lines}, pixels: {debug_pixels}")

    # For sensor model correction debug print, only print for the very first pixel of the first line once
    first_pixel_debug_printed = False
    
    results = []
    nan_intersection_count = 0
    d_world_z_threshold = 1e-6

    for i in range(num_lines):
        if i % 100 == 0:
            print(f"Verarbeite HSI-Zeile {i+1}/{num_lines}...")
            
        pose_index_to_use = num_lines - 1 - i
        current_pose = poses_df.iloc[pose_index_to_use]
        if i in debug_lines:
             print(f"  INFO: For HSI line {i}, using pose index {pose_index_to_use}. Timestamp: {current_pose.get('timestamp', 'N/A')}")

        P_imu_world = np.array([current_pose['pos_x'], current_pose['pos_y'], current_pose['pos_z']])
        q_body_to_world_xyzw = np.array([current_pose['quat_x'], current_pose['quat_y'], 
                                         current_pose['quat_z'], current_pose['quat_w']])
        try:
            R_body_to_world = Rotation.from_quat(q_body_to_world_xyzw).as_matrix()
            R_sensor_to_world = R_body_to_world @ R_sensor_to_body
        except Exception as e:
            print(f"Fehler bei Quaternion-Konvertierung in Zeile {i}: {q_body_to_world_xyzw}, Fehler: {e}")
            for j_err in range(num_samples):
                 results.append({'hsi_line_index': i, 'pixel_index': j_err, 'X_ground': np.nan, 'Y_ground': np.nan, 'Z_ground': np.nan})
            continue

        P_sensor_world = P_imu_world + R_body_to_world @ effective_lever_arm_body # Use effective_lever_arm_body

        for j in range(num_samples):
            original_vinkelx_rad = vinkelx_rad_all_pixels[j] # Renamed
            vinkely_rad = vinkely_rad_all_pixels[j]

            # Apply sensor model correction for vinkelx_rad
            corrected_vinkelx_rad = (original_vinkelx_rad * scale_vinkel_x) + offset_vinkel_x

            if not first_pixel_debug_printed and i == 0 and j == 0:
                print(f"DEBUG: Sensor Correction - Loaded scale_vinkel_x: {scale_vinkel_x}, offset_vinkel_x: {offset_vinkel_x}")
                print(f"DEBUG: Sensor Correction - Pixel (L{i},P{j}): Original vinkelx_rad: {original_vinkelx_rad:.6f}, Corrected vinkelx_rad: {corrected_vinkelx_rad:.6f}")
                first_pixel_debug_printed = True

            # Use corrected_vinkelx_rad for d_sensor_frame calculation
            dx = np.sin(corrected_vinkelx_rad) * np.cos(vinkely_rad)
            dy = np.sin(vinkely_rad) # vinkely_rad is not corrected by these parameters
            dz = np.cos(corrected_vinkelx_rad) * np.cos(vinkely_rad)
            d_sensor_frame = np.array([dx, dy, dz])
            
            norm_d_sensor_frame = np.linalg.norm(d_sensor_frame)
            if not np.isclose(norm_d_sensor_frame, 1.0):
                if norm_d_sensor_frame > 1e-9:
                    d_sensor_frame = d_sensor_frame / norm_d_sensor_frame
                else: 
                    d_sensor_frame = np.array([0,0,1])

            d_world = R_sensor_to_world @ d_sensor_frame
            
            X_ground, Y_ground, Z_ground_coord = np.nan, np.nan, np.nan

            if z_ground_method == "dsm_intersection":
                if dsm_interpolator and dsm_bounds:
                    X_ground, Y_ground, Z_ground_coord = calculate_ray_dsm_intersection(
                        P_sensor_world, d_world, dsm_interpolator, dsm_bounds, dsm_nodata_value,
                        ray_max_dist, ray_initial_step, ray_bisection_tol
                    )
                    if np.isnan(X_ground): # If X is NaN, assume intersection failed
                        nan_intersection_count += 1
                else: # Should not happen if check for dsm_interpolator was done before loop
                    nan_intersection_count += 1
            else: 
                if abs(d_world[2]) > d_world_z_threshold:
                    t = (Z_ground_flat_plane - P_sensor_world[2]) / d_world[2]
                    if t >= 0:
                        X_ground = P_sensor_world[0] + t * d_world[0]
                        Y_ground = P_sensor_world[1] + t * d_world[1]
                        Z_ground_coord = Z_ground_flat_plane
                    else:
                        nan_intersection_count +=1
                else:
                    nan_intersection_count +=1
            
            results.append({
                'hsi_line_index': i,
                'pixel_index': j,
                'X_ground': X_ground,
                'Y_ground': Y_ground,
                'Z_ground': Z_ground_coord 
            })

            if i in debug_lines and j in debug_pixels:
                if j == min(list(debug_pixels)) if debug_pixels else False:
                    print(f"\n--- Debugging HSI Line {i} (Pose Index {pose_index_to_use}) ---")
                    print(f"  P_sensor_world: {P_sensor_world}")
                    print(f"  R_sensor_to_world:\n{R_sensor_to_world}")
                print(f"    --- Pixel {j} ---")
                print(f"    orig_vx_rad: {original_vinkelx_rad:.5f}, corr_vx_rad={corrected_vinkelx_rad:.5f}, vinkely_rad: {vinkely_rad:.5f}")
                print(f"    d_sensor_frame (using corrected_vinkelx): {d_sensor_frame}")
                print(f"    d_world: {d_world}")
                print(f"    Intersection (X,Y,Z): ({X_ground:.3f}, {Y_ground:.3f}, {Z_ground_coord:.3f})")

    results_df = pd.DataFrame(results)
    print(f"\nVerarbeitung abgeschlossen. {nan_intersection_count} von {num_lines*num_samples} Pixeln konnten nicht auf Grund projiziert werden (NaN).")
    
    # Calculate and print mean yaw angle from poses
    if 'yaw_deg' in poses_df.columns:
        mean_yaw_deg = poses_df['yaw_deg'].mean()
        print(f"Mittlerer Yaw-Winkel aus Posen: {mean_yaw_deg:.2f} Grad")
    else:
        print("WARNUNG: Spalte 'yaw_deg' nicht in Posen-DataFrame gefunden. Mittlerer Yaw kann nicht berechnet werden.")

    print(f"Speichere georeferenzierte Pixelkoordinaten nach: {output_file_path}")
    try:
        results_df.to_csv(output_file_path, index=False, float_format='%.5f')
        print("Datei erfolgreich gespeichert.")
        return True # Success
    except Exception as e:
        print(f"FEHLER beim Speichern der CSV-Datei: {e}")
        return False # Failure

if __name__ == "__main__":
    DEFAULT_CONFIG_PATH = 'config.toml'
    print(f"Führe direkte Georeferenzierung der HSI Pixel aus mit Konfiguration: {DEFAULT_CONFIG_PATH}")
    success = run_georeferencing(config_path=DEFAULT_CONFIG_PATH)
    if success:
        print("Direkte Georeferenzierung der HSI Pixel erfolgreich abgeschlossen.")
    else:
        print("Fehler bei der direkten Georeferenzierung der HSI Pixel.")