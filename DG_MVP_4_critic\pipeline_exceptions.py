class PipelineError(Exception):
    """Base class for exceptions in this pipeline."""
    pass

class PipelineConfigError(PipelineError):
    """Exception raised for errors in the configuration."""
    pass

class HSIDataError(PipelineError):
    """Exception raised for errors related to HSI data (header, image, sync files)."""
    pass

class SynchronizationError(PipelineError):
    """Exception raised for errors during HSI and WebODM pose synchronization."""
    pass

class GeoreferencingError(PipelineError):
    """Exception raised for errors during the georeferencing process."""
    pass

class DSMIntersectionError(GeoreferencingError):
    """Exception raised for errors during DSM intersection calculation."""
    pass

class PlottingError(PipelineError):
    """Exception raised for errors during plot generation."""
    pass