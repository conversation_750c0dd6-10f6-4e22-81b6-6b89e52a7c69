import logging
from pipeline_exceptions import PipelineError, PipelineConfigError, HSIDataError, SynchronizationError
# It's generally good practice for modules to define their own loggers.
# The main_pipeline.py will handle the overall setup_logging.
logger = logging.getLogger(__name__)
import json
import numpy as np
import pandas as pd
from scipy.spatial.transform import Rotation, Slerp
from datetime import datetime, timezone
import os
import toml

# --- Helper Functions ---

def parse_hdr_file(hdr_file_path):
    """
    Parses the ENVI header file to extract metadata.
    Specifically looks for 'lines' and 'OffsetBetweenMainAntennaAndTargetPoint'.
    """
    metadata = {}
    try:
        with open(hdr_file_path, "r") as f:
            for line in f:
                line = line.strip()
                if "=" in line:
                    key_value = line.split("=", 1)
                    key = key_value[0].strip()
                    value = key_value[1].strip().replace("{", "").replace("}", "")
                    if key == "lines":
                        metadata["lines"] = int(value)
                    elif key == "OffsetBetweenMainAntennaAndTargetPoint (x,y,z)":
                        try:
                            coords_str = (
                                value.replace("(", "").replace(")", "").split(",")
                            )
                            metadata["lever_arm_xyz_mm"] = [
                                int(c.strip()) for c in coords_str
                            ]
                        except ValueError:
                            print(f"Warning: Could not parse lever arm: {value}")
                            metadata["lever_arm_xyz_mm"] = None
    except FileNotFoundError:
        print(f"Error: Header file not found: {hdr_file_path}")
        return None
    except Exception as e:
        print(f"Error parsing header file {hdr_file_path}: {e}")
        return None
    if "lines" not in metadata:
        print(f"Warning: 'lines' not found in {hdr_file_path}")
    return metadata


def convert_hsi_timestamp_to_ns(timestamp_str):
    """
    Converts HSI timestamps (assumed to be in microseconds) to nanoseconds.
    """
    return int(timestamp_str) * 1000  # HSI sync file timestamps are in microseconds


def load_hsi_data(sync_file_path, total_lines_from_hdr):
    """
    Loads HSI synchronization data.
    Timestamps are expected to be in nanoseconds.
    Frame/Line numbers are 1-based and in reverse order in the file.
    Corrects line numbers to be 0-based and in acquisition order.
    Returns a list of tuples: (corrected_hsi_line_index, hsi_timestamp_ns)
    sorted by timestamp.
    """
    hsi_data = []
    try:
        with open(sync_file_path, "r") as f:
            lines = f.readlines()
            if not lines or "Frame/Line" not in lines[0]:
                print(f"Error: Invalid HSI sync file format: {sync_file_path}")
                return []

            # Skip header
            for line_content in lines[1:]:
                parts = line_content.strip().split()
                if len(parts) == 2:
                    try:
                        original_line_num = int(parts[0])
                        timestamp_ns = convert_hsi_timestamp_to_ns(parts[1])
                        hsi_data.append(
                            {
                                "original_line_num": original_line_num,
                                "timestamp_ns": timestamp_ns,
                            }
                        )
                    except ValueError:
                        print(
                            f"Warning: Skipping invalid line in HSI sync file: {line_content.strip()}"
                        )
                        continue
                else:
                    print(
                        f"Warning: Skipping malformed line in HSI sync file: {line_content.strip()}"
                    )

        if not hsi_data:
            print("No data parsed from HSI sync file.")
            return []

        hsi_data.sort(key=lambda x: x["timestamp_ns"])

        processed_hsi_data = []
        for i, record in enumerate(hsi_data):
            corrected_hsi_line_index = i
            if corrected_hsi_line_index >= total_lines_from_hdr:
                print(
                    f"Warning: Corrected HSI line index {corrected_hsi_line_index} exceeds total lines from HDR {total_lines_from_hdr-1}. Original line num: {record['original_line_num']}"
                )
            processed_hsi_data.append(
                (corrected_hsi_line_index, record["timestamp_ns"])
            )

        if len(processed_hsi_data) != total_lines_from_hdr:
            print(
                f"Warning: Number of lines in sync file ({len(processed_hsi_data)}) does not match 'lines' in HDR ({total_lines_from_hdr})."
            )
        return processed_hsi_data

    except FileNotFoundError:
        print(f"Error: HSI sync file not found: {sync_file_path}")
        return []
    except Exception as e:
        print(f"Error loading HSI data from {sync_file_path}: {e}")
        return []


def load_webodm_data(webodm_csv_path):
    """
    Loads WebODM pose data from the consolidated CSV file.
    Extracts timestamp, translation, and rotation.
    Timestamp ('haip_timestamp_ns') is already in nanoseconds.
    Returns a list of dictionaries: {'timestamp_ns': ..., 'translation': ..., 'rotation_vec': ...}
    sorted by timestamp.
    """
    webodm_poses = []
    try:
        df = pd.read_csv(webodm_csv_path)
        required_cols = [
            "haip_timestamp_ns",
            "pos_x", "pos_y", "pos_z",
            "rot_1", "rot_2", "rot_3",
        ]
        missing_cols = [col for col in required_cols if col not in df.columns]
        if missing_cols:
            print(
                f"Error: Missing required columns in {webodm_csv_path}: {', '.join(missing_cols)}"
            )
            return []

        df = df.sort_values(by="haip_timestamp_ns")

        for index, row in df.iterrows():
            try:
                timestamp_ns = int(row["haip_timestamp_ns"])
                translation = np.array([row["pos_x"], row["pos_y"], row["pos_z"]])
                rotation_vec = np.array([row["rot_1"], row["rot_2"], row["rot_3"]])
                webodm_poses.append(
                    {
                        "timestamp_ns": timestamp_ns,
                        "translation": translation,
                        "rotation_vec": rotation_vec,
                        "original_filename": row.get("image_filename", "N/A"),
                    }
                )
            except ValueError as ve:
                print(
                    f"Warning: Skipping row {index} in {webodm_csv_path} due to data conversion error: {ve}"
                )
                continue
            except Exception as e:
                print(
                    f"Warning: Skipping row {index} in {webodm_csv_path} due to unexpected error: {e}"
                )
                continue
        return webodm_poses

    except FileNotFoundError:
        print(f"Error: WebODM CSV file not found: {webodm_csv_path}")
        return []
    except pd.errors.EmptyDataError:
        print(f"Error: WebODM CSV file is empty: {webodm_csv_path}")
        return []
    except Exception as e:
        print(f"Error loading WebODM data from {webodm_csv_path}: {e}")
        return []


def interpolate_pose(hsi_timestamp_ns, webodm_poses):
    """
    Interpolates camera pose (translation and rotation) for a given HSI timestamp.
    Linear interpolation for translation, SLERP for rotation.
    Calculates the absolute time differences to the previous (ts_before) and next (ts_after) WebODM timestamps.
    Handles edge cases where HSI timestamp is outside WebODM timestamp range.
    Returns: (translation, rotation_object, delta_t_prev_ns, delta_t_next_ns)
    """
    if not webodm_poses:
        return None, None, np.nan, np.nan

    p_before = None
    p_after = None

    for pose in webodm_poses:
        if pose["timestamp_ns"] <= hsi_timestamp_ns:
            p_before = pose
        else:
            p_after = pose
            break

    if p_before is None:
        ts_first_webodm = webodm_poses[0]["timestamp_ns"]
        delta_t_prev = np.nan
        delta_t_next = float(abs(hsi_timestamp_ns - ts_first_webodm))
        return (
            webodm_poses[0]["translation"],
            Rotation.from_rotvec(webodm_poses[0]["rotation_vec"]),
            delta_t_prev,
            delta_t_next,
        )

    if p_after is None:
        ts_last_webodm = webodm_poses[-1]["timestamp_ns"]
        delta_t_prev = float(abs(hsi_timestamp_ns - ts_last_webodm))
        delta_t_next = np.nan
        return (
            webodm_poses[-1]["translation"],
            Rotation.from_rotvec(webodm_poses[-1]["rotation_vec"]),
            delta_t_prev,
            delta_t_next,
        )

    ts_b = p_before["timestamp_ns"]
    ts_a = p_after["timestamp_ns"]
    delta_t_prev = float(abs(hsi_timestamp_ns - ts_b))
    delta_t_next = float(abs(hsi_timestamp_ns - ts_a))

    if ts_b == ts_a:
        return (
            p_before["translation"],
            Rotation.from_rotvec(p_before["rotation_vec"]),
            delta_t_prev,
            delta_t_next,
        )

    if ts_b == hsi_timestamp_ns:
        return (
            p_before["translation"],
            Rotation.from_rotvec(p_before["rotation_vec"]),
            delta_t_prev,
            delta_t_next,
        )

    t_diff_total = ts_a - ts_b
    alpha = (hsi_timestamp_ns - ts_b) / t_diff_total

    interp_translation = (1 - alpha) * p_before["translation"] + alpha * p_after["translation"]

    try:
        rot_before = Rotation.from_rotvec(p_before["rotation_vec"])
        rot_after = Rotation.from_rotvec(p_after["rotation_vec"])
    except ValueError as e:
        print(f"Error converting rotation vector for SLERP: {e}")
        print(f"p_before rot_vec: {p_before['rotation_vec']}, p_after rot_vec: {p_after['rotation_vec']}")
        fallback_rot = rot_before if alpha < 0.5 else rot_after
        return interp_translation, fallback_rot, delta_t_prev, delta_t_next

    key_rots = Rotation.concatenate([rot_before, rot_after])
    key_times = [0, 1]
    slerp_interpolator = Slerp(key_times, key_rots)
    interp_rotation = slerp_interpolator([alpha])[0]
    return interp_translation, interp_rotation, delta_t_prev, delta_t_next


def run_synchronization(config_path: str) -> bool:
    """
    Main processing logic for HSI and WebODM data synchronization.
    Reads configuration, loads data, interpolates poses, and saves results.
    Returns True if successful, False otherwise.
    """
    print(f"Starting HSI-WebODM synchronization with config: {config_path}")

    # --- Configuration Loading ---
    try:
        config = toml.load(config_path)
    except FileNotFoundError:
        print(f"Fehler: Konfigurationsdatei {config_path} nicht gefunden.")
        return False
    except Exception as e:
        print(f"Fehler beim Laden der Konfigurationsdatei {config_path}: {e}")
        return False

    # --- Path Definitions from Configuration ---
    try:
        hsi_data_dir = config['paths']['hsi_data_directory']
        hsi_base_filename = config['paths']['hsi_base_filename']
        hsi_sync_filename_stem = hsi_base_filename.replace("_cont", "")
        hsi_sync_file = os.path.join(hsi_data_dir, f"{hsi_sync_filename_stem}_sync.txt")
        hsi_hdr_file = os.path.join(hsi_data_dir, f"{hsi_base_filename}.hdr")

        output_dir_from_config = config['paths']['output_directory']
        consolidated_poses_filename = config['paths']['consolidated_webodm_poses_csv']
        webodm_csv_file = os.path.join(output_dir_from_config, consolidated_poses_filename)

        hsi_poses_output_filename = config['paths']['hsi_poses_csv']
        output_csv_file = os.path.join(output_dir_from_config, hsi_poses_output_filename)
    except KeyError as e:
        print(f"Fehler: Fehlender Schlüssel in der Konfigurationsdatei {config_path}: {e}")
        return False

    # Ensure output directory from config exists
    try:
        os.makedirs(output_dir_from_config, exist_ok=True)
    except OSError as e:
        print(f"Fehler beim Erstellen des Ausgabeordners {output_dir_from_config}: {e}")
        return False

    # 1. Load HSI Metadata
    print(f"Loading HSI header file: {hsi_hdr_file}")
    hsi_metadata = parse_hdr_file(hsi_hdr_file)
    if hsi_metadata is None or "lines" not in hsi_metadata:
        print("Error: Could not load essential HSI metadata (number of lines).")
        return False
    total_hsi_lines = hsi_metadata["lines"]
    lever_arm_mm = hsi_metadata.get("lever_arm_xyz_mm")
    print(f"Total HSI lines from HDR: {total_hsi_lines}")
    if lever_arm_mm:
        print(f"Lever arm (x,y,z) in mm: {lever_arm_mm}")
        print("Note: Lever arm correction is NOT implemented in this PoC script.")
    else:
        print("Lever arm information not found or not parsed from HDR.")

    # 2. Load HSI Synchronization Data
    print(f"Loading HSI sync data from: {hsi_sync_file}")
    hsi_sync_data = load_hsi_data(hsi_sync_file, total_hsi_lines)
    if not hsi_sync_data:
        print("Error: No HSI sync data loaded.")
        return False
    print(f"Loaded {len(hsi_sync_data)} HSI linescans with timestamps.")

    # 3. Load WebODM Pose Data
    print(f"Loading WebODM pose data from: {webodm_csv_file}")
    webodm_poses = load_webodm_data(webodm_csv_file)
    if not webodm_poses:
        print("Error: No WebODM pose data loaded.")
        return False
    print(f"Loaded {len(webodm_poses)} WebODM poses.")

    # 4. Synchronize and Interpolate
    print("Synchronizing HSI linescans with WebODM poses...")
    output_data = []
    for hsi_line_index, hsi_ts_ns in hsi_sync_data:
        (
            interp_translation,
            interp_rotation_obj,
            time_diff_prev_ns,
            time_diff_next_ns,
        ) = interpolate_pose(hsi_ts_ns, webodm_poses)

        if interp_translation is not None and interp_rotation_obj is not None:
            quat = interp_rotation_obj.as_quat()  # [x, y, z, w]
            time_diff_prev_ms = time_diff_prev_ns / 1_000_000.0 if not np.isnan(time_diff_prev_ns) else np.nan
            time_diff_next_ms = time_diff_next_ns / 1_000_000.0 if not np.isnan(time_diff_next_ns) else np.nan
            output_data.append(
                {
                    "hsi_line_index": hsi_line_index,
                    "hsi_timestamp_ns": hsi_ts_ns,
                    "pos_x": interp_translation[0],
                    "pos_y": interp_translation[1],
                    "pos_z": interp_translation[2],
                    "quat_x": quat[0],
                    "quat_y": quat[1],
                    "quat_z": quat[2],
                    "quat_w": quat[3],
                    "time_diff_to_prev_ms": time_diff_prev_ms,
                    "time_diff_to_next_ms": time_diff_next_ms,
                }
            )
        else:
            print(
                f"Warning: Could not interpolate pose for HSI line {hsi_line_index} (timestamp {hsi_ts_ns})"
            )

    # 5. Save Output
    if not output_data:
        print("No data to save. Output file will not be created.")
        # Depending on requirements, this could be a `return False`
        # For now, if processing reached here without critical errors, consider it a partial success.
        # However, the task implies creating hsi_poses.csv.
        return True # Or False if an output file is strictly required. Let's assume True for now if no other errors.

    df_output = pd.DataFrame(output_data)
    try:
        df_output.to_csv(output_csv_file, index=False)
        print(f"Successfully synchronized and saved HSI poses to {output_csv_file}")
    except Exception as e:
        print(f"Error saving output CSV to {output_csv_file}: {e}")
        return False

    print("HSI-WebODM synchronization script finished.")
    return True


if __name__ == "__main__":
    DEFAULT_CONFIG_PATH = 'config.toml'
    print(f"Führe HSI Posen Synchronisation aus mit Konfiguration: {DEFAULT_CONFIG_PATH}")
    success = run_synchronization(config_path=DEFAULT_CONFIG_PATH)
    if success:
        print("HSI Posen Synchronisation erfolgreich abgeschlossen.")
    else:
        print("Fehler bei der HSI Posen Synchronisation.")
