import toml
import logging
from pathlib import Path
from typing import Dict, Any

from create_consolidated_webodm_poses import run_consolidation
from synchronize_hsi_webodm import run_synchronization
from georeference_hsi_pixels import run_georeferencing
from create_georeferenced_rgb import run_create_rgb_geotiff
from plot_hsi_data import run_plotting
from logging_config import setup_logging, get_logger
from pipeline_exceptions import (
    PipelineError, PipelineConfigError, HSIDataError,
    SynchronizationError, GeoreferencingError
)

def load_pipeline_config(config_path: str) -> Dict[str, Any]:
    """
    Load and validate the pipeline configuration file.

    Args:
        config_path: Path to the TOML configuration file

    Returns:
        Loaded configuration dictionary

    Raises:
        PipelineConfigError: If config file cannot be loaded or is invalid
    """
    logger = get_logger(__name__)

    try:
        config_file = Path(config_path)
        if not config_file.exists():
            raise PipelineConfigError(f"Configuration file not found: {config_path}")

        with open(config_file, 'r', encoding='utf-8') as f:
            config = toml.load(f)

        logger.info(f"Successfully loaded configuration from: {config_path}")
        return config

    except toml.TomlDecodeError as e:
        raise PipelineConfigError(f"Invalid TOML syntax in config file: {e}") from e
    except Exception as e:
        raise PipelineConfigError(f"Error loading configuration file: {e}") from e


def run_complete_pipeline(config_path: str = 'config.toml') -> bool:
    """
    Execute the complete HSI Direct Georeferencing Pipeline.

    Args:
        config_path: Path to the TOML configuration file. Default is 'config.toml'.

    Returns:
        True if the pipeline completed successfully, False otherwise.
    """

    # Setup logging first
    setup_logging(log_level="INFO", log_file="pipeline.log")
    logger = get_logger(__name__)

    logger.info(f"Starting complete HSI Georeferencing Pipeline with configuration: {config_path}")
    pipeline_successful = True

    try:
        # Load configuration once
        config = load_pipeline_config(config_path)

        # Step 1: WebODM Pose Consolidation
        logger.info("--- Step 1: WebODM Pose Consolidation ---")
        try:
            if not run_consolidation(config):
                raise PipelineError("WebODM pose consolidation failed")
            logger.info("Step 1 completed successfully.")
        except Exception as e:
            logger.error(f"ERROR in Step 1: WebODM Pose Consolidation. {e}")
            return False

        # Step 2: HSI Pose Synchronization
        logger.info("--- Step 2: HSI Pose Synchronization ---")
        try:
            if not run_synchronization(config):
                raise SynchronizationError("HSI pose synchronization failed")
            logger.info("Step 2 completed successfully.")
        except Exception as e:
            logger.error(f"ERROR in Step 2: HSI Pose Synchronization. {e}")
            return False

        # Step 3: Direct HSI Pixel Georeferencing
        logger.info("--- Step 3: Direct HSI Pixel Georeferencing ---")
        try:
            if not run_georeferencing(config):
                raise GeoreferencingError("HSI pixel georeferencing failed")
            logger.info("Step 3 completed successfully.")
        except Exception as e:
            logger.error(f"ERROR in Step 3: Direct HSI Pixel Georeferencing. {e}")
            return False

        # Step 4: Georeferenced RGB GeoTIFF Creation (optional)
        logger.info("--- Step 4: Georeferenced RGB GeoTIFF Creation ---")
        try:
            if not run_create_rgb_geotiff(config):
                logger.warning("Step 4: Error creating georeferenced RGB GeoTIFF.")
                pipeline_successful = False  # Mark as not fully successful
            else:
                logger.info("Step 4 completed successfully.")
        except Exception as e:
            logger.warning(f"Step 4: Error creating georeferenced RGB GeoTIFF: {e}")
            pipeline_successful = False

        # Step 5: Plot Generation (optional)
        logger.info("--- Step 5: Plot Generation ---")
        try:
            if not run_plotting(config):
                logger.warning("Step 5: Error generating plots.")
                pipeline_successful = False  # Mark as not fully successful
            else:
                logger.info("Step 5 completed successfully.")
        except Exception as e:
            logger.warning(f"Step 5: Error generating plots: {e}")
            pipeline_successful = False

        if pipeline_successful:
            logger.info("Complete HSI Georeferencing Pipeline executed successfully.")
        else:
            logger.warning("HSI Georeferencing Pipeline completed with warnings or errors in optional steps.")

    except PipelineConfigError as e:
        logger.error(f"Configuration error: {e}")
        return False
    except Exception as e:
        logger.error(f"Unexpected error in pipeline: {e}", exc_info=True)
        return False

    return pipeline_successful

if __name__ == "__main__":
    # Future enhancement: Add command-line arguments for config_path or step selection
    # Example with argparse:
    # import argparse
    # parser = argparse.ArgumentParser(description="Execute the HSI Georeferencing Pipeline.")
    # parser.add_argument('--config', type=str, default='config.toml',
    #                     help='Path to configuration file (default: config.toml)')
    # args = parser.parse_args()
    # run_complete_pipeline(config_path=args.config)

    success = run_complete_pipeline()
    if success:
        print("Pipeline execution completed: Success")
    else:
        print("Pipeline execution completed: With errors/warnings")