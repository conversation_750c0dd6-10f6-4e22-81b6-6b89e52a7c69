2025-06-02 13:18:42 - logging_config - INFO - logging_config:72 - Logging configured with level: INFO, file: pipeline.log
2025-06-02 13:18:42 - test - INFO - <string>:1 - Testing logging setup
2025-06-02 15:33:54 - logging_config - INFO - logging_config:72 - Logging configured with level: INFO, file: pipeline.log
2025-06-02 15:33:54 - main_pipeline - INFO - main_pipeline:64 - Starting complete HSI Georeferencing Pipeline with configuration: test_config.toml
2025-06-02 15:33:54 - main_pipeline - INFO - main_pipeline:72 - --- Step 1: WebODM Pose Consolidation ---
2025-06-02 15:33:54 - main_pipeline - INFO - main_pipeline:76 - Step 1 completed successfully.
2025-06-02 15:33:54 - main_pipeline - INFO - main_pipeline:82 - --- Step 2: HSI Pose Synchronization ---
2025-06-02 15:33:54 - main_pipeline - INFO - main_pipeline:86 - Step 2 completed successfully.
2025-06-02 15:33:54 - main_pipeline - INFO - main_pipeline:92 - --- Step 3: Direct HSI Pixel Georeferencing ---
2025-06-02 15:33:54 - main_pipeline - INFO - main_pipeline:96 - Step 3 completed successfully.
2025-06-02 15:33:54 - main_pipeline - INFO - main_pipeline:102 - --- Step 4: Georeferenced RGB GeoTIFF Creation ---
2025-06-02 15:33:54 - main_pipeline - INFO - main_pipeline:108 - Step 4 completed successfully.
2025-06-02 15:33:54 - main_pipeline - INFO - main_pipeline:114 - --- Step 5: Plot Generation ---
2025-06-02 15:33:54 - main_pipeline - INFO - main_pipeline:120 - Step 5 completed successfully.
2025-06-02 15:33:54 - main_pipeline - INFO - main_pipeline:126 - Complete HSI Georeferencing Pipeline executed successfully.
2025-06-02 15:33:54 - logging_config - INFO - logging_config:72 - Logging configured with level: INFO, file: pipeline.log
2025-06-02 15:33:54 - main_pipeline - INFO - main_pipeline:64 - Starting complete HSI Georeferencing Pipeline with configuration: test_config.toml
2025-06-02 15:33:54 - main_pipeline - INFO - main_pipeline:72 - --- Step 1: WebODM Pose Consolidation ---
2025-06-02 15:33:54 - main_pipeline - INFO - main_pipeline:76 - Step 1 completed successfully.
2025-06-02 15:33:54 - main_pipeline - INFO - main_pipeline:82 - --- Step 2: HSI Pose Synchronization ---
2025-06-02 15:33:54 - main_pipeline - ERROR - main_pipeline:88 - ERROR in Step 2: HSI Pose Synchronization. HSI pose synchronization failed
2025-06-02 15:33:54 - logging_config - INFO - logging_config:72 - Logging configured with level: INFO, file: pipeline.log
2025-06-02 15:33:54 - main_pipeline - INFO - main_pipeline:64 - Starting complete HSI Georeferencing Pipeline with configuration: test_config.toml
2025-06-02 15:33:54 - main_pipeline - INFO - main_pipeline:72 - --- Step 1: WebODM Pose Consolidation ---
2025-06-02 15:33:54 - main_pipeline - INFO - main_pipeline:76 - Step 1 completed successfully.
2025-06-02 15:33:54 - main_pipeline - INFO - main_pipeline:82 - --- Step 2: HSI Pose Synchronization ---
2025-06-02 15:33:54 - main_pipeline - INFO - main_pipeline:86 - Step 2 completed successfully.
2025-06-02 15:33:54 - main_pipeline - INFO - main_pipeline:92 - --- Step 3: Direct HSI Pixel Georeferencing ---
2025-06-02 15:33:54 - main_pipeline - INFO - main_pipeline:96 - Step 3 completed successfully.
2025-06-02 15:33:54 - main_pipeline - INFO - main_pipeline:102 - --- Step 4: Georeferenced RGB GeoTIFF Creation ---
2025-06-02 15:33:54 - main_pipeline - WARNING - main_pipeline:105 - Step 4: Error creating georeferenced RGB GeoTIFF.
2025-06-02 15:33:54 - main_pipeline - INFO - main_pipeline:114 - --- Step 5: Plot Generation ---
2025-06-02 15:33:54 - main_pipeline - WARNING - main_pipeline:117 - Step 5: Error generating plots.
2025-06-02 15:33:54 - main_pipeline - WARNING - main_pipeline:128 - HSI Georeferencing Pipeline completed with warnings or errors in optional steps.
2025-06-02 15:33:54 - logging_config - INFO - logging_config:72 - Logging configured with level: INFO, file: pipeline.log
2025-06-02 15:33:54 - main_pipeline - INFO - main_pipeline:64 - Starting complete HSI Georeferencing Pipeline with configuration: invalid_config.toml
2025-06-02 15:33:54 - main_pipeline - ERROR - main_pipeline:131 - Configuration error: Config error
2025-06-02 15:33:54 - logging_config - INFO - logging_config:72 - Logging configured with level: INFO, file: pipeline.log
2025-06-02 15:33:54 - main_pipeline - INFO - main_pipeline:64 - Starting complete HSI Georeferencing Pipeline with configuration: test_config.toml
2025-06-02 15:33:54 - main_pipeline - INFO - main_pipeline:72 - --- Step 1: WebODM Pose Consolidation ---
2025-06-02 15:33:54 - main_pipeline - ERROR - main_pipeline:78 - ERROR in Step 1: WebODM Pose Consolidation. Unexpected error
2025-06-02 15:33:54 - logging_config - INFO - logging_config:72 - Logging configured with level: INFO, file: pipeline.log
2025-06-02 15:33:54 - main_pipeline - INFO - main_pipeline:64 - Starting complete HSI Georeferencing Pipeline with configuration: test_config.toml
2025-06-02 15:33:54 - main_pipeline - INFO - main_pipeline:72 - --- Step 1: WebODM Pose Consolidation ---
2025-06-02 15:33:54 - main_pipeline - INFO - main_pipeline:76 - Step 1 completed successfully.
2025-06-02 15:33:54 - main_pipeline - INFO - main_pipeline:82 - --- Step 2: HSI Pose Synchronization ---
2025-06-02 15:33:54 - main_pipeline - INFO - main_pipeline:86 - Step 2 completed successfully.
2025-06-02 15:33:54 - main_pipeline - INFO - main_pipeline:92 - --- Step 3: Direct HSI Pixel Georeferencing ---
2025-06-02 15:33:54 - main_pipeline - INFO - main_pipeline:96 - Step 3 completed successfully.
2025-06-02 15:33:54 - main_pipeline - INFO - main_pipeline:102 - --- Step 4: Georeferenced RGB GeoTIFF Creation ---
2025-06-02 15:33:54 - main_pipeline - INFO - main_pipeline:108 - Step 4 completed successfully.
2025-06-02 15:33:54 - main_pipeline - INFO - main_pipeline:114 - --- Step 5: Plot Generation ---
2025-06-02 15:33:54 - main_pipeline - INFO - main_pipeline:120 - Step 5 completed successfully.
2025-06-02 15:33:54 - main_pipeline - INFO - main_pipeline:126 - Complete HSI Georeferencing Pipeline executed successfully.
2025-06-02 15:34:08 - logging_config - INFO - logging_config:72 - Logging configured with level: INFO, file: pipeline.log
2025-06-02 15:34:08 - integration_test - INFO - <string>:13 - Starting integration test
2025-06-02 15:34:08 - lever_arm_utils - INFO - lever_arm_utils:66 - Using lever arm from HSI header: [ 0.12  0.08 -0.05]
2025-06-02 15:34:08 - integration_test - INFO - <string>:19 - Effective lever arm: [ 0.12  0.08 -0.05], Override used: False
2025-06-02 15:34:08 - integration_test - INFO - <string>:27 - Calculated 5 sensor view vectors
2025-06-02 15:34:08 - integration_test - INFO - <string>:28 - First vector: [0.09981345 0.01999867 0.99480517]
2025-06-02 15:34:08 - integration_test - INFO - <string>:29 - Vector norms: [1. 1. 1. 1. 1.]
2025-06-02 16:08:53 - logging_config - INFO - logging_config:72 - Logging configured with level: INFO, file: pipeline.log
2025-06-02 16:11:11 - logging_config - INFO - logging_config:72 - Logging configured with level: INFO, file: pipeline.log
2025-06-02 16:11:11 - main_pipeline - INFO - main_pipeline:64 - Starting complete HSI Georeferencing Pipeline with configuration: test_config.toml
2025-06-02 16:11:11 - main_pipeline - INFO - main_pipeline:72 - --- Step 1: WebODM Pose Consolidation ---
2025-06-02 16:11:11 - main_pipeline - INFO - main_pipeline:76 - Step 1 completed successfully.
2025-06-02 16:11:11 - main_pipeline - INFO - main_pipeline:82 - --- Step 2: HSI Pose Synchronization ---
2025-06-02 16:11:11 - main_pipeline - INFO - main_pipeline:86 - Step 2 completed successfully.
2025-06-02 16:11:11 - main_pipeline - INFO - main_pipeline:92 - --- Step 3: Direct HSI Pixel Georeferencing ---
2025-06-02 16:11:11 - main_pipeline - INFO - main_pipeline:96 - Step 3 completed successfully.
2025-06-02 16:11:11 - main_pipeline - INFO - main_pipeline:102 - --- Step 4: Georeferenced RGB GeoTIFF Creation ---
2025-06-02 16:11:11 - main_pipeline - INFO - main_pipeline:108 - Step 4 completed successfully.
2025-06-02 16:11:11 - main_pipeline - INFO - main_pipeline:114 - --- Step 5: Plot Generation ---
2025-06-02 16:11:11 - main_pipeline - INFO - main_pipeline:120 - Step 5 completed successfully.
2025-06-02 16:11:11 - main_pipeline - INFO - main_pipeline:126 - Complete HSI Georeferencing Pipeline executed successfully.
2025-06-02 16:11:11 - logging_config - INFO - logging_config:72 - Logging configured with level: INFO, file: pipeline.log
2025-06-02 16:11:11 - main_pipeline - INFO - main_pipeline:64 - Starting complete HSI Georeferencing Pipeline with configuration: test_config.toml
2025-06-02 16:11:11 - main_pipeline - INFO - main_pipeline:72 - --- Step 1: WebODM Pose Consolidation ---
2025-06-02 16:11:11 - main_pipeline - INFO - main_pipeline:76 - Step 1 completed successfully.
2025-06-02 16:11:11 - main_pipeline - INFO - main_pipeline:82 - --- Step 2: HSI Pose Synchronization ---
2025-06-02 16:11:11 - main_pipeline - ERROR - main_pipeline:88 - ERROR in Step 2: HSI Pose Synchronization. HSI pose synchronization failed
2025-06-02 16:11:11 - logging_config - INFO - logging_config:72 - Logging configured with level: INFO, file: pipeline.log
2025-06-02 16:11:11 - main_pipeline - INFO - main_pipeline:64 - Starting complete HSI Georeferencing Pipeline with configuration: test_config.toml
2025-06-02 16:11:11 - main_pipeline - INFO - main_pipeline:72 - --- Step 1: WebODM Pose Consolidation ---
2025-06-02 16:11:11 - main_pipeline - INFO - main_pipeline:76 - Step 1 completed successfully.
2025-06-02 16:11:11 - main_pipeline - INFO - main_pipeline:82 - --- Step 2: HSI Pose Synchronization ---
2025-06-02 16:11:11 - main_pipeline - INFO - main_pipeline:86 - Step 2 completed successfully.
2025-06-02 16:11:11 - main_pipeline - INFO - main_pipeline:92 - --- Step 3: Direct HSI Pixel Georeferencing ---
2025-06-02 16:11:11 - main_pipeline - INFO - main_pipeline:96 - Step 3 completed successfully.
2025-06-02 16:11:11 - main_pipeline - INFO - main_pipeline:102 - --- Step 4: Georeferenced RGB GeoTIFF Creation ---
2025-06-02 16:11:11 - main_pipeline - WARNING - main_pipeline:105 - Step 4: Error creating georeferenced RGB GeoTIFF.
2025-06-02 16:11:11 - main_pipeline - INFO - main_pipeline:114 - --- Step 5: Plot Generation ---
2025-06-02 16:11:11 - main_pipeline - WARNING - main_pipeline:117 - Step 5: Error generating plots.
2025-06-02 16:11:11 - main_pipeline - WARNING - main_pipeline:128 - HSI Georeferencing Pipeline completed with warnings or errors in optional steps.
2025-06-02 16:11:11 - logging_config - INFO - logging_config:72 - Logging configured with level: INFO, file: pipeline.log
2025-06-02 16:11:11 - main_pipeline - INFO - main_pipeline:64 - Starting complete HSI Georeferencing Pipeline with configuration: invalid_config.toml
2025-06-02 16:11:11 - main_pipeline - ERROR - main_pipeline:131 - Configuration error: Config error
2025-06-02 16:11:11 - logging_config - INFO - logging_config:72 - Logging configured with level: INFO, file: pipeline.log
2025-06-02 16:11:11 - main_pipeline - INFO - main_pipeline:64 - Starting complete HSI Georeferencing Pipeline with configuration: test_config.toml
2025-06-02 16:11:11 - main_pipeline - INFO - main_pipeline:72 - --- Step 1: WebODM Pose Consolidation ---
2025-06-02 16:11:11 - main_pipeline - ERROR - main_pipeline:78 - ERROR in Step 1: WebODM Pose Consolidation. Unexpected error
2025-06-02 16:11:11 - logging_config - INFO - logging_config:72 - Logging configured with level: INFO, file: pipeline.log
2025-06-02 16:11:11 - main_pipeline - INFO - main_pipeline:64 - Starting complete HSI Georeferencing Pipeline with configuration: test_config.toml
2025-06-02 16:11:11 - main_pipeline - INFO - main_pipeline:72 - --- Step 1: WebODM Pose Consolidation ---
2025-06-02 16:11:11 - main_pipeline - INFO - main_pipeline:76 - Step 1 completed successfully.
2025-06-02 16:11:11 - main_pipeline - INFO - main_pipeline:82 - --- Step 2: HSI Pose Synchronization ---
2025-06-02 16:11:11 - main_pipeline - INFO - main_pipeline:86 - Step 2 completed successfully.
2025-06-02 16:11:11 - main_pipeline - INFO - main_pipeline:92 - --- Step 3: Direct HSI Pixel Georeferencing ---
2025-06-02 16:11:11 - main_pipeline - INFO - main_pipeline:96 - Step 3 completed successfully.
2025-06-02 16:11:11 - main_pipeline - INFO - main_pipeline:102 - --- Step 4: Georeferenced RGB GeoTIFF Creation ---
2025-06-02 16:11:11 - main_pipeline - INFO - main_pipeline:108 - Step 4 completed successfully.
2025-06-02 16:11:11 - main_pipeline - INFO - main_pipeline:114 - --- Step 5: Plot Generation ---
2025-06-02 16:11:11 - main_pipeline - INFO - main_pipeline:120 - Step 5 completed successfully.
2025-06-02 16:11:11 - main_pipeline - INFO - main_pipeline:126 - Complete HSI Georeferencing Pipeline executed successfully.
2025-06-02 16:11:11 - create_consolidated_webodm_poses - INFO - create_consolidated_webodm_poses:34 - Starting WebODM pose consolidation
2025-06-02 16:11:11 - create_consolidated_webodm_poses - INFO - create_consolidated_webodm_poses:52 - WebODM data directory: test_dir
2025-06-02 16:11:11 - create_consolidated_webodm_poses - INFO - create_consolidated_webodm_poses:53 - Shots GeoJSON file: test_dir\shots.geojson
2025-06-02 16:11:11 - create_consolidated_webodm_poses - INFO - create_consolidated_webodm_poses:54 - HAIP files directory: test_dir\haip
2025-06-02 16:11:11 - create_consolidated_webodm_poses - INFO - create_consolidated_webodm_poses:55 - Output CSV file: output\consolidated.csv
2025-06-02 16:11:11 - create_consolidated_webodm_poses - INFO - create_consolidated_webodm_poses:63 - Output directory created/verified: output
2025-06-02 16:11:11 - create_consolidated_webodm_poses - INFO - create_consolidated_webodm_poses:75 - Loading shots GeoJSON file: test_dir\shots.geojson
2025-06-02 16:11:11 - create_consolidated_webodm_poses - INFO - create_consolidated_webodm_poses:34 - Starting WebODM pose consolidation
2025-06-02 16:11:11 - create_consolidated_webodm_poses - INFO - create_consolidated_webodm_poses:34 - Starting WebODM pose consolidation
2025-06-02 16:11:11 - create_consolidated_webodm_poses - INFO - create_consolidated_webodm_poses:52 - WebODM data directory: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-75\test_missing_shots_file_raises0
2025-06-02 16:11:11 - create_consolidated_webodm_poses - INFO - create_consolidated_webodm_poses:53 - Shots GeoJSON file: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-75\test_missing_shots_file_raises0\nonexistent_shots.geojson
2025-06-02 16:11:11 - create_consolidated_webodm_poses - INFO - create_consolidated_webodm_poses:54 - HAIP files directory: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-75\test_missing_shots_file_raises0\haip
2025-06-02 16:11:11 - create_consolidated_webodm_poses - INFO - create_consolidated_webodm_poses:55 - Output CSV file: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-75\test_missing_shots_file_raises0\output\consolidated.csv
2025-06-02 16:11:11 - create_consolidated_webodm_poses - INFO - create_consolidated_webodm_poses:63 - Output directory created/verified: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-75\test_missing_shots_file_raises0\output
2025-06-02 16:11:11 - create_consolidated_webodm_poses - INFO - create_consolidated_webodm_poses:75 - Loading shots GeoJSON file: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-75\test_missing_shots_file_raises0\nonexistent_shots.geojson
2025-06-02 16:11:11 - create_consolidated_webodm_poses - INFO - create_consolidated_webodm_poses:34 - Starting WebODM pose consolidation
2025-06-02 16:11:11 - create_consolidated_webodm_poses - INFO - create_consolidated_webodm_poses:52 - WebODM data directory: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-75\test_invalid_json_raises_webod0
2025-06-02 16:11:11 - create_consolidated_webodm_poses - INFO - create_consolidated_webodm_poses:53 - Shots GeoJSON file: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-75\test_invalid_json_raises_webod0\shots.geojson
2025-06-02 16:11:11 - create_consolidated_webodm_poses - INFO - create_consolidated_webodm_poses:54 - HAIP files directory: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-75\test_invalid_json_raises_webod0\haip
2025-06-02 16:11:11 - create_consolidated_webodm_poses - INFO - create_consolidated_webodm_poses:55 - Output CSV file: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-75\test_invalid_json_raises_webod0\output\consolidated.csv
2025-06-02 16:11:11 - create_consolidated_webodm_poses - INFO - create_consolidated_webodm_poses:63 - Output directory created/verified: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-75\test_invalid_json_raises_webod0\output
2025-06-02 16:11:11 - create_consolidated_webodm_poses - INFO - create_consolidated_webodm_poses:75 - Loading shots GeoJSON file: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-75\test_invalid_json_raises_webod0\shots.geojson
2025-06-02 16:11:11 - create_consolidated_webodm_poses - INFO - create_consolidated_webodm_poses:34 - Starting WebODM pose consolidation
2025-06-02 16:11:11 - create_consolidated_webodm_poses - INFO - create_consolidated_webodm_poses:52 - WebODM data directory: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-75\test_missing_features_key_rais0
2025-06-02 16:11:11 - create_consolidated_webodm_poses - INFO - create_consolidated_webodm_poses:53 - Shots GeoJSON file: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-75\test_missing_features_key_rais0\shots.geojson
2025-06-02 16:11:11 - create_consolidated_webodm_poses - INFO - create_consolidated_webodm_poses:54 - HAIP files directory: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-75\test_missing_features_key_rais0\haip
2025-06-02 16:11:11 - create_consolidated_webodm_poses - INFO - create_consolidated_webodm_poses:55 - Output CSV file: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-75\test_missing_features_key_rais0\output\consolidated.csv
2025-06-02 16:11:11 - create_consolidated_webodm_poses - INFO - create_consolidated_webodm_poses:63 - Output directory created/verified: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-75\test_missing_features_key_rais0\output
2025-06-02 16:11:11 - create_consolidated_webodm_poses - INFO - create_consolidated_webodm_poses:75 - Loading shots GeoJSON file: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-75\test_missing_features_key_rais0\shots.geojson
2025-06-02 16:11:11 - create_consolidated_webodm_poses - INFO - create_consolidated_webodm_poses:34 - Starting WebODM pose consolidation
2025-06-02 16:11:11 - create_consolidated_webodm_poses - INFO - create_consolidated_webodm_poses:52 - WebODM data directory: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-75\test_successful_consolidation_0
2025-06-02 16:11:11 - create_consolidated_webodm_poses - INFO - create_consolidated_webodm_poses:53 - Shots GeoJSON file: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-75\test_successful_consolidation_0\shots.geojson
2025-06-02 16:11:11 - create_consolidated_webodm_poses - INFO - create_consolidated_webodm_poses:54 - HAIP files directory: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-75\test_successful_consolidation_0\haip
2025-06-02 16:11:11 - create_consolidated_webodm_poses - INFO - create_consolidated_webodm_poses:55 - Output CSV file: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-75\test_successful_consolidation_0\output\consolidated.csv
2025-06-02 16:11:11 - create_consolidated_webodm_poses - INFO - create_consolidated_webodm_poses:63 - Output directory created/verified: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-75\test_successful_consolidation_0\output
2025-06-02 16:11:11 - create_consolidated_webodm_poses - INFO - create_consolidated_webodm_poses:75 - Loading shots GeoJSON file: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-75\test_successful_consolidation_0\shots.geojson
2025-06-02 16:11:11 - create_consolidated_webodm_poses - INFO - create_consolidated_webodm_poses:89 - Found 1 features in shots GeoJSON
2025-06-02 16:11:11 - create_consolidated_webodm_poses - INFO - create_consolidated_webodm_poses:188 - Processing complete: 1 entries processed, 0 entries skipped
2025-06-02 16:11:11 - create_consolidated_webodm_poses - INFO - create_consolidated_webodm_poses:204 - Successfully created C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-75\test_successful_consolidation_0\output\consolidated.csv with 1 entries
2025-06-02 16:33:51 - logging_config - INFO - logging_config:72 - Logging configured with level: INFO, file: pipeline.log
2025-06-02 16:33:51 - main_pipeline - INFO - main_pipeline:64 - Starting complete HSI Georeferencing Pipeline with configuration: test_config.toml
2025-06-02 16:33:51 - main_pipeline - INFO - main_pipeline:72 - --- Step 1: WebODM Pose Consolidation ---
2025-06-02 16:33:51 - main_pipeline - INFO - main_pipeline:76 - Step 1 completed successfully.
2025-06-02 16:33:51 - main_pipeline - INFO - main_pipeline:82 - --- Step 2: HSI Pose Synchronization ---
2025-06-02 16:33:51 - main_pipeline - INFO - main_pipeline:86 - Step 2 completed successfully.
2025-06-02 16:33:51 - main_pipeline - INFO - main_pipeline:92 - --- Step 3: Direct HSI Pixel Georeferencing ---
2025-06-02 16:33:51 - main_pipeline - INFO - main_pipeline:96 - Step 3 completed successfully.
2025-06-02 16:33:51 - main_pipeline - INFO - main_pipeline:102 - --- Step 4: Georeferenced RGB GeoTIFF Creation ---
2025-06-02 16:33:51 - main_pipeline - INFO - main_pipeline:108 - Step 4 completed successfully.
2025-06-02 16:33:51 - main_pipeline - INFO - main_pipeline:114 - --- Step 5: Plot Generation ---
2025-06-02 16:33:51 - main_pipeline - INFO - main_pipeline:120 - Step 5 completed successfully.
2025-06-02 16:33:51 - main_pipeline - INFO - main_pipeline:126 - Complete HSI Georeferencing Pipeline executed successfully.
2025-06-02 16:33:51 - logging_config - INFO - logging_config:72 - Logging configured with level: INFO, file: pipeline.log
2025-06-02 16:33:51 - main_pipeline - INFO - main_pipeline:64 - Starting complete HSI Georeferencing Pipeline with configuration: test_config.toml
2025-06-02 16:33:51 - main_pipeline - INFO - main_pipeline:72 - --- Step 1: WebODM Pose Consolidation ---
2025-06-02 16:33:51 - main_pipeline - INFO - main_pipeline:76 - Step 1 completed successfully.
2025-06-02 16:33:51 - main_pipeline - INFO - main_pipeline:82 - --- Step 2: HSI Pose Synchronization ---
2025-06-02 16:33:51 - main_pipeline - ERROR - main_pipeline:88 - ERROR in Step 2: HSI Pose Synchronization. HSI pose synchronization failed
2025-06-02 16:33:51 - logging_config - INFO - logging_config:72 - Logging configured with level: INFO, file: pipeline.log
2025-06-02 16:33:51 - main_pipeline - INFO - main_pipeline:64 - Starting complete HSI Georeferencing Pipeline with configuration: test_config.toml
2025-06-02 16:33:51 - main_pipeline - INFO - main_pipeline:72 - --- Step 1: WebODM Pose Consolidation ---
2025-06-02 16:33:51 - main_pipeline - INFO - main_pipeline:76 - Step 1 completed successfully.
2025-06-02 16:33:51 - main_pipeline - INFO - main_pipeline:82 - --- Step 2: HSI Pose Synchronization ---
2025-06-02 16:33:51 - main_pipeline - INFO - main_pipeline:86 - Step 2 completed successfully.
2025-06-02 16:33:51 - main_pipeline - INFO - main_pipeline:92 - --- Step 3: Direct HSI Pixel Georeferencing ---
2025-06-02 16:33:51 - main_pipeline - INFO - main_pipeline:96 - Step 3 completed successfully.
2025-06-02 16:33:51 - main_pipeline - INFO - main_pipeline:102 - --- Step 4: Georeferenced RGB GeoTIFF Creation ---
2025-06-02 16:33:51 - main_pipeline - WARNING - main_pipeline:105 - Step 4: Error creating georeferenced RGB GeoTIFF.
2025-06-02 16:33:51 - main_pipeline - INFO - main_pipeline:114 - --- Step 5: Plot Generation ---
2025-06-02 16:33:51 - main_pipeline - WARNING - main_pipeline:117 - Step 5: Error generating plots.
2025-06-02 16:33:51 - main_pipeline - WARNING - main_pipeline:128 - HSI Georeferencing Pipeline completed with warnings or errors in optional steps.
2025-06-02 16:33:51 - logging_config - INFO - logging_config:72 - Logging configured with level: INFO, file: pipeline.log
2025-06-02 16:33:51 - main_pipeline - INFO - main_pipeline:64 - Starting complete HSI Georeferencing Pipeline with configuration: invalid_config.toml
2025-06-02 16:33:51 - main_pipeline - ERROR - main_pipeline:131 - Configuration error: Config error
2025-06-02 16:33:51 - logging_config - INFO - logging_config:72 - Logging configured with level: INFO, file: pipeline.log
2025-06-02 16:33:51 - main_pipeline - INFO - main_pipeline:64 - Starting complete HSI Georeferencing Pipeline with configuration: test_config.toml
2025-06-02 16:33:51 - main_pipeline - INFO - main_pipeline:72 - --- Step 1: WebODM Pose Consolidation ---
2025-06-02 16:33:51 - main_pipeline - ERROR - main_pipeline:78 - ERROR in Step 1: WebODM Pose Consolidation. Unexpected error
2025-06-02 16:33:51 - logging_config - INFO - logging_config:72 - Logging configured with level: INFO, file: pipeline.log
2025-06-02 16:33:51 - main_pipeline - INFO - main_pipeline:64 - Starting complete HSI Georeferencing Pipeline with configuration: test_config.toml
2025-06-02 16:33:51 - main_pipeline - INFO - main_pipeline:72 - --- Step 1: WebODM Pose Consolidation ---
2025-06-02 16:33:51 - main_pipeline - INFO - main_pipeline:76 - Step 1 completed successfully.
2025-06-02 16:33:51 - main_pipeline - INFO - main_pipeline:82 - --- Step 2: HSI Pose Synchronization ---
2025-06-02 16:33:51 - main_pipeline - INFO - main_pipeline:86 - Step 2 completed successfully.
2025-06-02 16:33:51 - main_pipeline - INFO - main_pipeline:92 - --- Step 3: Direct HSI Pixel Georeferencing ---
2025-06-02 16:33:51 - main_pipeline - INFO - main_pipeline:96 - Step 3 completed successfully.
2025-06-02 16:33:51 - main_pipeline - INFO - main_pipeline:102 - --- Step 4: Georeferenced RGB GeoTIFF Creation ---
2025-06-02 16:33:51 - main_pipeline - INFO - main_pipeline:108 - Step 4 completed successfully.
2025-06-02 16:33:51 - main_pipeline - INFO - main_pipeline:114 - --- Step 5: Plot Generation ---
2025-06-02 16:33:51 - main_pipeline - INFO - main_pipeline:120 - Step 5 completed successfully.
2025-06-02 16:33:51 - main_pipeline - INFO - main_pipeline:126 - Complete HSI Georeferencing Pipeline executed successfully.
2025-06-02 16:33:51 - create_consolidated_webodm_poses - INFO - create_consolidated_webodm_poses:34 - Starting WebODM pose consolidation
2025-06-02 16:33:51 - create_consolidated_webodm_poses - INFO - create_consolidated_webodm_poses:52 - WebODM data directory: test_dir
2025-06-02 16:33:51 - create_consolidated_webodm_poses - INFO - create_consolidated_webodm_poses:53 - Shots GeoJSON file: test_dir\shots.geojson
2025-06-02 16:33:51 - create_consolidated_webodm_poses - INFO - create_consolidated_webodm_poses:54 - HAIP files directory: test_dir\haip
2025-06-02 16:33:51 - create_consolidated_webodm_poses - INFO - create_consolidated_webodm_poses:55 - Output CSV file: output\consolidated.csv
2025-06-02 16:33:51 - create_consolidated_webodm_poses - INFO - create_consolidated_webodm_poses:63 - Output directory created/verified: output
2025-06-02 16:33:51 - create_consolidated_webodm_poses - INFO - create_consolidated_webodm_poses:75 - Loading shots GeoJSON file: test_dir\shots.geojson
2025-06-02 16:33:51 - create_consolidated_webodm_poses - INFO - create_consolidated_webodm_poses:34 - Starting WebODM pose consolidation
2025-06-02 16:33:51 - create_consolidated_webodm_poses - INFO - create_consolidated_webodm_poses:34 - Starting WebODM pose consolidation
2025-06-02 16:33:51 - create_consolidated_webodm_poses - INFO - create_consolidated_webodm_poses:52 - WebODM data directory: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-77\test_missing_shots_file_raises0
2025-06-02 16:33:51 - create_consolidated_webodm_poses - INFO - create_consolidated_webodm_poses:53 - Shots GeoJSON file: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-77\test_missing_shots_file_raises0\nonexistent_shots.geojson
2025-06-02 16:33:51 - create_consolidated_webodm_poses - INFO - create_consolidated_webodm_poses:54 - HAIP files directory: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-77\test_missing_shots_file_raises0\haip
2025-06-02 16:33:51 - create_consolidated_webodm_poses - INFO - create_consolidated_webodm_poses:55 - Output CSV file: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-77\test_missing_shots_file_raises0\output\consolidated.csv
2025-06-02 16:33:51 - create_consolidated_webodm_poses - INFO - create_consolidated_webodm_poses:63 - Output directory created/verified: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-77\test_missing_shots_file_raises0\output
2025-06-02 16:33:51 - create_consolidated_webodm_poses - INFO - create_consolidated_webodm_poses:75 - Loading shots GeoJSON file: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-77\test_missing_shots_file_raises0\nonexistent_shots.geojson
2025-06-02 16:33:51 - create_consolidated_webodm_poses - INFO - create_consolidated_webodm_poses:34 - Starting WebODM pose consolidation
2025-06-02 16:33:51 - create_consolidated_webodm_poses - INFO - create_consolidated_webodm_poses:52 - WebODM data directory: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-77\test_invalid_json_raises_webod0
2025-06-02 16:33:51 - create_consolidated_webodm_poses - INFO - create_consolidated_webodm_poses:53 - Shots GeoJSON file: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-77\test_invalid_json_raises_webod0\shots.geojson
2025-06-02 16:33:51 - create_consolidated_webodm_poses - INFO - create_consolidated_webodm_poses:54 - HAIP files directory: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-77\test_invalid_json_raises_webod0\haip
2025-06-02 16:33:51 - create_consolidated_webodm_poses - INFO - create_consolidated_webodm_poses:55 - Output CSV file: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-77\test_invalid_json_raises_webod0\output\consolidated.csv
2025-06-02 16:33:51 - create_consolidated_webodm_poses - INFO - create_consolidated_webodm_poses:63 - Output directory created/verified: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-77\test_invalid_json_raises_webod0\output
2025-06-02 16:33:51 - create_consolidated_webodm_poses - INFO - create_consolidated_webodm_poses:75 - Loading shots GeoJSON file: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-77\test_invalid_json_raises_webod0\shots.geojson
2025-06-02 16:33:51 - create_consolidated_webodm_poses - INFO - create_consolidated_webodm_poses:34 - Starting WebODM pose consolidation
2025-06-02 16:33:51 - create_consolidated_webodm_poses - INFO - create_consolidated_webodm_poses:52 - WebODM data directory: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-77\test_missing_features_key_rais0
2025-06-02 16:33:51 - create_consolidated_webodm_poses - INFO - create_consolidated_webodm_poses:53 - Shots GeoJSON file: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-77\test_missing_features_key_rais0\shots.geojson
2025-06-02 16:33:51 - create_consolidated_webodm_poses - INFO - create_consolidated_webodm_poses:54 - HAIP files directory: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-77\test_missing_features_key_rais0\haip
2025-06-02 16:33:51 - create_consolidated_webodm_poses - INFO - create_consolidated_webodm_poses:55 - Output CSV file: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-77\test_missing_features_key_rais0\output\consolidated.csv
2025-06-02 16:33:51 - create_consolidated_webodm_poses - INFO - create_consolidated_webodm_poses:63 - Output directory created/verified: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-77\test_missing_features_key_rais0\output
2025-06-02 16:33:51 - create_consolidated_webodm_poses - INFO - create_consolidated_webodm_poses:75 - Loading shots GeoJSON file: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-77\test_missing_features_key_rais0\shots.geojson
2025-06-02 16:33:51 - create_consolidated_webodm_poses - INFO - create_consolidated_webodm_poses:34 - Starting WebODM pose consolidation
2025-06-02 16:33:51 - create_consolidated_webodm_poses - INFO - create_consolidated_webodm_poses:52 - WebODM data directory: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-77\test_successful_consolidation_0
2025-06-02 16:33:51 - create_consolidated_webodm_poses - INFO - create_consolidated_webodm_poses:53 - Shots GeoJSON file: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-77\test_successful_consolidation_0\shots.geojson
2025-06-02 16:33:51 - create_consolidated_webodm_poses - INFO - create_consolidated_webodm_poses:54 - HAIP files directory: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-77\test_successful_consolidation_0\haip
2025-06-02 16:33:51 - create_consolidated_webodm_poses - INFO - create_consolidated_webodm_poses:55 - Output CSV file: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-77\test_successful_consolidation_0\output\consolidated.csv
2025-06-02 16:33:51 - create_consolidated_webodm_poses - INFO - create_consolidated_webodm_poses:63 - Output directory created/verified: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-77\test_successful_consolidation_0\output
2025-06-02 16:33:51 - create_consolidated_webodm_poses - INFO - create_consolidated_webodm_poses:75 - Loading shots GeoJSON file: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-77\test_successful_consolidation_0\shots.geojson
2025-06-02 16:33:51 - create_consolidated_webodm_poses - INFO - create_consolidated_webodm_poses:89 - Found 1 features in shots GeoJSON
2025-06-02 16:33:51 - create_consolidated_webodm_poses - INFO - create_consolidated_webodm_poses:188 - Processing complete: 1 entries processed, 0 entries skipped
2025-06-02 16:33:51 - create_consolidated_webodm_poses - INFO - create_consolidated_webodm_poses:204 - Successfully created C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-77\test_successful_consolidation_0\output\consolidated.csv with 1 entries
2025-06-02 16:33:51 - synchronize_hsi_webodm - INFO - synchronize_hsi_webodm:356 - Starting HSI-WebODM synchronization
2025-06-02 16:33:51 - synchronize_hsi_webodm - INFO - synchronize_hsi_webodm:373 - HSI data directory: test_dir
2025-06-02 16:33:51 - synchronize_hsi_webodm - INFO - synchronize_hsi_webodm:374 - HSI sync file: test_dir\test_hsi_sync.txt
2025-06-02 16:33:51 - synchronize_hsi_webodm - INFO - synchronize_hsi_webodm:375 - HSI header file: test_dir\test_hsi.hdr
2025-06-02 16:33:51 - synchronize_hsi_webodm - INFO - synchronize_hsi_webodm:376 - WebODM CSV file: output\webodm.csv
2025-06-02 16:33:51 - synchronize_hsi_webodm - INFO - synchronize_hsi_webodm:377 - Output CSV file: output\hsi_poses.csv
2025-06-02 16:33:51 - synchronize_hsi_webodm - INFO - synchronize_hsi_webodm:385 - Output directory created/verified: output
2025-06-02 16:33:51 - synchronize_hsi_webodm - INFO - synchronize_hsi_webodm:390 - Loading HSI header file: test_dir\test_hsi.hdr
2025-06-02 16:33:51 - synchronize_hsi_webodm - ERROR - synchronize_hsi_webodm:407 - Error loading HSI metadata: Test
2025-06-02 16:33:51 - synchronize_hsi_webodm - INFO - synchronize_hsi_webodm:356 - Starting HSI-WebODM synchronization
2025-06-02 16:33:51 - synchronize_hsi_webodm - INFO - synchronize_hsi_webodm:202 - Loaded 2 WebODM pose records from CSV
2025-06-02 16:33:51 - synchronize_hsi_webodm - INFO - synchronize_hsi_webodm:229 - Successfully processed 2 WebODM poses
2025-06-02 16:41:37 - logging_config - INFO - logging_config:72 - Logging configured with level: INFO, file: pipeline.log
2025-06-02 16:41:37 - main_pipeline - INFO - main_pipeline:64 - Starting complete HSI Georeferencing Pipeline with configuration: test_config.toml
2025-06-02 16:41:37 - main_pipeline - INFO - main_pipeline:72 - --- Step 1: WebODM Pose Consolidation ---
2025-06-02 16:41:37 - main_pipeline - INFO - main_pipeline:76 - Step 1 completed successfully.
2025-06-02 16:41:37 - main_pipeline - INFO - main_pipeline:82 - --- Step 2: HSI Pose Synchronization ---
2025-06-02 16:41:37 - main_pipeline - INFO - main_pipeline:86 - Step 2 completed successfully.
2025-06-02 16:41:37 - main_pipeline - INFO - main_pipeline:92 - --- Step 3: Direct HSI Pixel Georeferencing ---
2025-06-02 16:41:37 - main_pipeline - INFO - main_pipeline:96 - Step 3 completed successfully.
2025-06-02 16:41:37 - main_pipeline - INFO - main_pipeline:102 - --- Step 4: Georeferenced RGB GeoTIFF Creation ---
2025-06-02 16:41:37 - main_pipeline - INFO - main_pipeline:108 - Step 4 completed successfully.
2025-06-02 16:41:37 - main_pipeline - INFO - main_pipeline:114 - --- Step 5: Plot Generation ---
2025-06-02 16:41:37 - main_pipeline - INFO - main_pipeline:120 - Step 5 completed successfully.
2025-06-02 16:41:37 - main_pipeline - INFO - main_pipeline:126 - Complete HSI Georeferencing Pipeline executed successfully.
2025-06-02 16:41:38 - logging_config - INFO - logging_config:72 - Logging configured with level: INFO, file: pipeline.log
2025-06-02 16:41:38 - main_pipeline - INFO - main_pipeline:64 - Starting complete HSI Georeferencing Pipeline with configuration: test_config.toml
2025-06-02 16:41:38 - main_pipeline - INFO - main_pipeline:72 - --- Step 1: WebODM Pose Consolidation ---
2025-06-02 16:41:38 - main_pipeline - INFO - main_pipeline:76 - Step 1 completed successfully.
2025-06-02 16:41:38 - main_pipeline - INFO - main_pipeline:82 - --- Step 2: HSI Pose Synchronization ---
2025-06-02 16:41:38 - main_pipeline - ERROR - main_pipeline:88 - ERROR in Step 2: HSI Pose Synchronization. HSI pose synchronization failed
2025-06-02 16:41:38 - logging_config - INFO - logging_config:72 - Logging configured with level: INFO, file: pipeline.log
2025-06-02 16:41:38 - main_pipeline - INFO - main_pipeline:64 - Starting complete HSI Georeferencing Pipeline with configuration: test_config.toml
2025-06-02 16:41:38 - main_pipeline - INFO - main_pipeline:72 - --- Step 1: WebODM Pose Consolidation ---
2025-06-02 16:41:38 - main_pipeline - INFO - main_pipeline:76 - Step 1 completed successfully.
2025-06-02 16:41:38 - main_pipeline - INFO - main_pipeline:82 - --- Step 2: HSI Pose Synchronization ---
2025-06-02 16:41:38 - main_pipeline - INFO - main_pipeline:86 - Step 2 completed successfully.
2025-06-02 16:41:38 - main_pipeline - INFO - main_pipeline:92 - --- Step 3: Direct HSI Pixel Georeferencing ---
2025-06-02 16:41:38 - main_pipeline - INFO - main_pipeline:96 - Step 3 completed successfully.
2025-06-02 16:41:38 - main_pipeline - INFO - main_pipeline:102 - --- Step 4: Georeferenced RGB GeoTIFF Creation ---
2025-06-02 16:41:38 - main_pipeline - WARNING - main_pipeline:105 - Step 4: Error creating georeferenced RGB GeoTIFF.
2025-06-02 16:41:38 - main_pipeline - INFO - main_pipeline:114 - --- Step 5: Plot Generation ---
2025-06-02 16:41:38 - main_pipeline - WARNING - main_pipeline:117 - Step 5: Error generating plots.
2025-06-02 16:41:38 - main_pipeline - WARNING - main_pipeline:128 - HSI Georeferencing Pipeline completed with warnings or errors in optional steps.
2025-06-02 16:41:38 - logging_config - INFO - logging_config:72 - Logging configured with level: INFO, file: pipeline.log
2025-06-02 16:41:38 - main_pipeline - INFO - main_pipeline:64 - Starting complete HSI Georeferencing Pipeline with configuration: invalid_config.toml
2025-06-02 16:41:38 - main_pipeline - ERROR - main_pipeline:131 - Configuration error: Config error
2025-06-02 16:41:38 - logging_config - INFO - logging_config:72 - Logging configured with level: INFO, file: pipeline.log
2025-06-02 16:41:38 - main_pipeline - INFO - main_pipeline:64 - Starting complete HSI Georeferencing Pipeline with configuration: test_config.toml
2025-06-02 16:41:38 - main_pipeline - INFO - main_pipeline:72 - --- Step 1: WebODM Pose Consolidation ---
2025-06-02 16:41:38 - main_pipeline - ERROR - main_pipeline:78 - ERROR in Step 1: WebODM Pose Consolidation. Unexpected error
2025-06-02 16:41:38 - logging_config - INFO - logging_config:72 - Logging configured with level: INFO, file: pipeline.log
2025-06-02 16:41:38 - main_pipeline - INFO - main_pipeline:64 - Starting complete HSI Georeferencing Pipeline with configuration: test_config.toml
2025-06-02 16:41:38 - main_pipeline - INFO - main_pipeline:72 - --- Step 1: WebODM Pose Consolidation ---
2025-06-02 16:41:38 - main_pipeline - INFO - main_pipeline:76 - Step 1 completed successfully.
2025-06-02 16:41:38 - main_pipeline - INFO - main_pipeline:82 - --- Step 2: HSI Pose Synchronization ---
2025-06-02 16:41:38 - main_pipeline - INFO - main_pipeline:86 - Step 2 completed successfully.
2025-06-02 16:41:38 - main_pipeline - INFO - main_pipeline:92 - --- Step 3: Direct HSI Pixel Georeferencing ---
2025-06-02 16:41:38 - main_pipeline - INFO - main_pipeline:96 - Step 3 completed successfully.
2025-06-02 16:41:38 - main_pipeline - INFO - main_pipeline:102 - --- Step 4: Georeferenced RGB GeoTIFF Creation ---
2025-06-02 16:41:38 - main_pipeline - INFO - main_pipeline:108 - Step 4 completed successfully.
2025-06-02 16:41:38 - main_pipeline - INFO - main_pipeline:114 - --- Step 5: Plot Generation ---
2025-06-02 16:41:38 - main_pipeline - INFO - main_pipeline:120 - Step 5 completed successfully.
2025-06-02 16:41:38 - main_pipeline - INFO - main_pipeline:126 - Complete HSI Georeferencing Pipeline executed successfully.
2025-06-02 16:41:38 - create_consolidated_webodm_poses - INFO - create_consolidated_webodm_poses:34 - Starting WebODM pose consolidation
2025-06-02 16:41:38 - create_consolidated_webodm_poses - INFO - create_consolidated_webodm_poses:52 - WebODM data directory: test_dir
2025-06-02 16:41:38 - create_consolidated_webodm_poses - INFO - create_consolidated_webodm_poses:53 - Shots GeoJSON file: test_dir\shots.geojson
2025-06-02 16:41:38 - create_consolidated_webodm_poses - INFO - create_consolidated_webodm_poses:54 - HAIP files directory: test_dir\haip
2025-06-02 16:41:38 - create_consolidated_webodm_poses - INFO - create_consolidated_webodm_poses:55 - Output CSV file: output\consolidated.csv
2025-06-02 16:41:38 - create_consolidated_webodm_poses - INFO - create_consolidated_webodm_poses:63 - Output directory created/verified: output
2025-06-02 16:41:38 - create_consolidated_webodm_poses - INFO - create_consolidated_webodm_poses:75 - Loading shots GeoJSON file: test_dir\shots.geojson
2025-06-02 16:41:38 - create_consolidated_webodm_poses - INFO - create_consolidated_webodm_poses:34 - Starting WebODM pose consolidation
2025-06-02 16:41:38 - create_consolidated_webodm_poses - INFO - create_consolidated_webodm_poses:34 - Starting WebODM pose consolidation
2025-06-02 16:41:38 - create_consolidated_webodm_poses - INFO - create_consolidated_webodm_poses:52 - WebODM data directory: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-79\test_missing_shots_file_raises0
2025-06-02 16:41:38 - create_consolidated_webodm_poses - INFO - create_consolidated_webodm_poses:53 - Shots GeoJSON file: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-79\test_missing_shots_file_raises0\nonexistent_shots.geojson
2025-06-02 16:41:38 - create_consolidated_webodm_poses - INFO - create_consolidated_webodm_poses:54 - HAIP files directory: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-79\test_missing_shots_file_raises0\haip
2025-06-02 16:41:38 - create_consolidated_webodm_poses - INFO - create_consolidated_webodm_poses:55 - Output CSV file: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-79\test_missing_shots_file_raises0\output\consolidated.csv
2025-06-02 16:41:38 - create_consolidated_webodm_poses - INFO - create_consolidated_webodm_poses:63 - Output directory created/verified: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-79\test_missing_shots_file_raises0\output
2025-06-02 16:41:38 - create_consolidated_webodm_poses - INFO - create_consolidated_webodm_poses:75 - Loading shots GeoJSON file: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-79\test_missing_shots_file_raises0\nonexistent_shots.geojson
2025-06-02 16:41:38 - create_consolidated_webodm_poses - INFO - create_consolidated_webodm_poses:34 - Starting WebODM pose consolidation
2025-06-02 16:41:38 - create_consolidated_webodm_poses - INFO - create_consolidated_webodm_poses:52 - WebODM data directory: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-79\test_invalid_json_raises_webod0
2025-06-02 16:41:38 - create_consolidated_webodm_poses - INFO - create_consolidated_webodm_poses:53 - Shots GeoJSON file: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-79\test_invalid_json_raises_webod0\shots.geojson
2025-06-02 16:41:38 - create_consolidated_webodm_poses - INFO - create_consolidated_webodm_poses:54 - HAIP files directory: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-79\test_invalid_json_raises_webod0\haip
2025-06-02 16:41:38 - create_consolidated_webodm_poses - INFO - create_consolidated_webodm_poses:55 - Output CSV file: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-79\test_invalid_json_raises_webod0\output\consolidated.csv
2025-06-02 16:41:38 - create_consolidated_webodm_poses - INFO - create_consolidated_webodm_poses:63 - Output directory created/verified: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-79\test_invalid_json_raises_webod0\output
2025-06-02 16:41:38 - create_consolidated_webodm_poses - INFO - create_consolidated_webodm_poses:75 - Loading shots GeoJSON file: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-79\test_invalid_json_raises_webod0\shots.geojson
2025-06-02 16:41:38 - create_consolidated_webodm_poses - INFO - create_consolidated_webodm_poses:34 - Starting WebODM pose consolidation
2025-06-02 16:41:38 - create_consolidated_webodm_poses - INFO - create_consolidated_webodm_poses:52 - WebODM data directory: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-79\test_missing_features_key_rais0
2025-06-02 16:41:38 - create_consolidated_webodm_poses - INFO - create_consolidated_webodm_poses:53 - Shots GeoJSON file: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-79\test_missing_features_key_rais0\shots.geojson
2025-06-02 16:41:38 - create_consolidated_webodm_poses - INFO - create_consolidated_webodm_poses:54 - HAIP files directory: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-79\test_missing_features_key_rais0\haip
2025-06-02 16:41:38 - create_consolidated_webodm_poses - INFO - create_consolidated_webodm_poses:55 - Output CSV file: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-79\test_missing_features_key_rais0\output\consolidated.csv
2025-06-02 16:41:38 - create_consolidated_webodm_poses - INFO - create_consolidated_webodm_poses:63 - Output directory created/verified: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-79\test_missing_features_key_rais0\output
2025-06-02 16:41:38 - create_consolidated_webodm_poses - INFO - create_consolidated_webodm_poses:75 - Loading shots GeoJSON file: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-79\test_missing_features_key_rais0\shots.geojson
2025-06-02 16:41:38 - create_consolidated_webodm_poses - INFO - create_consolidated_webodm_poses:34 - Starting WebODM pose consolidation
2025-06-02 16:41:38 - create_consolidated_webodm_poses - INFO - create_consolidated_webodm_poses:52 - WebODM data directory: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-79\test_successful_consolidation_0
2025-06-02 16:41:38 - create_consolidated_webodm_poses - INFO - create_consolidated_webodm_poses:53 - Shots GeoJSON file: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-79\test_successful_consolidation_0\shots.geojson
2025-06-02 16:41:38 - create_consolidated_webodm_poses - INFO - create_consolidated_webodm_poses:54 - HAIP files directory: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-79\test_successful_consolidation_0\haip
2025-06-02 16:41:38 - create_consolidated_webodm_poses - INFO - create_consolidated_webodm_poses:55 - Output CSV file: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-79\test_successful_consolidation_0\output\consolidated.csv
2025-06-02 16:41:38 - create_consolidated_webodm_poses - INFO - create_consolidated_webodm_poses:63 - Output directory created/verified: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-79\test_successful_consolidation_0\output
2025-06-02 16:41:38 - create_consolidated_webodm_poses - INFO - create_consolidated_webodm_poses:75 - Loading shots GeoJSON file: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-79\test_successful_consolidation_0\shots.geojson
2025-06-02 16:41:38 - create_consolidated_webodm_poses - INFO - create_consolidated_webodm_poses:89 - Found 1 features in shots GeoJSON
2025-06-02 16:41:38 - create_consolidated_webodm_poses - INFO - create_consolidated_webodm_poses:188 - Processing complete: 1 entries processed, 0 entries skipped
2025-06-02 16:41:38 - create_consolidated_webodm_poses - INFO - create_consolidated_webodm_poses:204 - Successfully created C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-79\test_successful_consolidation_0\output\consolidated.csv with 1 entries
2025-06-02 16:41:38 - synchronize_hsi_webodm - INFO - synchronize_hsi_webodm:356 - Starting HSI-WebODM synchronization
2025-06-02 16:41:38 - synchronize_hsi_webodm - INFO - synchronize_hsi_webodm:373 - HSI data directory: test_dir
2025-06-02 16:41:38 - synchronize_hsi_webodm - INFO - synchronize_hsi_webodm:374 - HSI sync file: test_dir\test_hsi_sync.txt
2025-06-02 16:41:38 - synchronize_hsi_webodm - INFO - synchronize_hsi_webodm:375 - HSI header file: test_dir\test_hsi.hdr
2025-06-02 16:41:38 - synchronize_hsi_webodm - INFO - synchronize_hsi_webodm:376 - WebODM CSV file: output\webodm.csv
2025-06-02 16:41:38 - synchronize_hsi_webodm - INFO - synchronize_hsi_webodm:377 - Output CSV file: output\hsi_poses.csv
2025-06-02 16:41:38 - synchronize_hsi_webodm - INFO - synchronize_hsi_webodm:385 - Output directory created/verified: output
2025-06-02 16:41:38 - synchronize_hsi_webodm - INFO - synchronize_hsi_webodm:390 - Loading HSI header file: test_dir\test_hsi.hdr
2025-06-02 16:41:38 - synchronize_hsi_webodm - ERROR - synchronize_hsi_webodm:407 - Error loading HSI metadata: Test
2025-06-02 16:41:38 - synchronize_hsi_webodm - INFO - synchronize_hsi_webodm:356 - Starting HSI-WebODM synchronization
2025-06-02 16:41:39 - synchronize_hsi_webodm - INFO - synchronize_hsi_webodm:202 - Loaded 2 WebODM pose records from CSV
2025-06-02 16:41:39 - synchronize_hsi_webodm - INFO - synchronize_hsi_webodm:229 - Successfully processed 2 WebODM poses
2025-06-02 17:28:21 - logging_config - INFO - logging_config:72 - Logging configured with level: INFO, file: pipeline.log
2025-06-02 17:28:21 - main_pipeline - INFO - main_pipeline:64 - Starting complete HSI Georeferencing Pipeline with configuration: test_config.toml
2025-06-02 17:28:21 - main_pipeline - INFO - main_pipeline:72 - --- Step 1: WebODM Pose Consolidation ---
2025-06-02 17:28:21 - main_pipeline - INFO - main_pipeline:76 - Step 1 completed successfully.
2025-06-02 17:28:21 - main_pipeline - INFO - main_pipeline:82 - --- Step 2: HSI Pose Synchronization ---
2025-06-02 17:28:21 - main_pipeline - INFO - main_pipeline:86 - Step 2 completed successfully.
2025-06-02 17:28:21 - main_pipeline - INFO - main_pipeline:92 - --- Step 3: Direct HSI Pixel Georeferencing ---
2025-06-02 17:28:21 - main_pipeline - INFO - main_pipeline:96 - Step 3 completed successfully.
2025-06-02 17:28:21 - main_pipeline - INFO - main_pipeline:102 - --- Step 4: Georeferenced RGB GeoTIFF Creation ---
2025-06-02 17:28:21 - main_pipeline - INFO - main_pipeline:108 - Step 4 completed successfully.
2025-06-02 17:28:21 - main_pipeline - INFO - main_pipeline:114 - --- Step 5: Plot Generation ---
2025-06-02 17:28:21 - main_pipeline - INFO - main_pipeline:120 - Step 5 completed successfully.
2025-06-02 17:28:21 - main_pipeline - INFO - main_pipeline:126 - Complete HSI Georeferencing Pipeline executed successfully.
2025-06-02 17:28:21 - logging_config - INFO - logging_config:72 - Logging configured with level: INFO, file: pipeline.log
2025-06-02 17:28:21 - main_pipeline - INFO - main_pipeline:64 - Starting complete HSI Georeferencing Pipeline with configuration: test_config.toml
2025-06-02 17:28:21 - main_pipeline - INFO - main_pipeline:72 - --- Step 1: WebODM Pose Consolidation ---
2025-06-02 17:28:21 - main_pipeline - INFO - main_pipeline:76 - Step 1 completed successfully.
2025-06-02 17:28:21 - main_pipeline - INFO - main_pipeline:82 - --- Step 2: HSI Pose Synchronization ---
2025-06-02 17:28:21 - main_pipeline - ERROR - main_pipeline:88 - ERROR in Step 2: HSI Pose Synchronization. HSI pose synchronization failed
2025-06-02 17:28:21 - logging_config - INFO - logging_config:72 - Logging configured with level: INFO, file: pipeline.log
2025-06-02 17:28:21 - main_pipeline - INFO - main_pipeline:64 - Starting complete HSI Georeferencing Pipeline with configuration: test_config.toml
2025-06-02 17:28:21 - main_pipeline - INFO - main_pipeline:72 - --- Step 1: WebODM Pose Consolidation ---
2025-06-02 17:28:21 - main_pipeline - INFO - main_pipeline:76 - Step 1 completed successfully.
2025-06-02 17:28:21 - main_pipeline - INFO - main_pipeline:82 - --- Step 2: HSI Pose Synchronization ---
2025-06-02 17:28:21 - main_pipeline - INFO - main_pipeline:86 - Step 2 completed successfully.
2025-06-02 17:28:21 - main_pipeline - INFO - main_pipeline:92 - --- Step 3: Direct HSI Pixel Georeferencing ---
2025-06-02 17:28:21 - main_pipeline - INFO - main_pipeline:96 - Step 3 completed successfully.
2025-06-02 17:28:21 - main_pipeline - INFO - main_pipeline:102 - --- Step 4: Georeferenced RGB GeoTIFF Creation ---
2025-06-02 17:28:21 - main_pipeline - WARNING - main_pipeline:105 - Step 4: Error creating georeferenced RGB GeoTIFF.
2025-06-02 17:28:21 - main_pipeline - INFO - main_pipeline:114 - --- Step 5: Plot Generation ---
2025-06-02 17:28:21 - main_pipeline - WARNING - main_pipeline:117 - Step 5: Error generating plots.
2025-06-02 17:28:21 - main_pipeline - WARNING - main_pipeline:128 - HSI Georeferencing Pipeline completed with warnings or errors in optional steps.
2025-06-02 17:28:21 - logging_config - INFO - logging_config:72 - Logging configured with level: INFO, file: pipeline.log
2025-06-02 17:28:21 - main_pipeline - INFO - main_pipeline:64 - Starting complete HSI Georeferencing Pipeline with configuration: invalid_config.toml
2025-06-02 17:28:21 - main_pipeline - ERROR - main_pipeline:131 - Configuration error: Config error
2025-06-02 17:28:21 - logging_config - INFO - logging_config:72 - Logging configured with level: INFO, file: pipeline.log
2025-06-02 17:28:21 - main_pipeline - INFO - main_pipeline:64 - Starting complete HSI Georeferencing Pipeline with configuration: test_config.toml
2025-06-02 17:28:21 - main_pipeline - INFO - main_pipeline:72 - --- Step 1: WebODM Pose Consolidation ---
2025-06-02 17:28:21 - main_pipeline - ERROR - main_pipeline:78 - ERROR in Step 1: WebODM Pose Consolidation. Unexpected error
2025-06-02 17:28:21 - logging_config - INFO - logging_config:72 - Logging configured with level: INFO, file: pipeline.log
2025-06-02 17:28:21 - main_pipeline - INFO - main_pipeline:64 - Starting complete HSI Georeferencing Pipeline with configuration: test_config.toml
2025-06-02 17:28:21 - main_pipeline - INFO - main_pipeline:72 - --- Step 1: WebODM Pose Consolidation ---
2025-06-02 17:28:21 - main_pipeline - INFO - main_pipeline:76 - Step 1 completed successfully.
2025-06-02 17:28:21 - main_pipeline - INFO - main_pipeline:82 - --- Step 2: HSI Pose Synchronization ---
2025-06-02 17:28:21 - main_pipeline - INFO - main_pipeline:86 - Step 2 completed successfully.
2025-06-02 17:28:21 - main_pipeline - INFO - main_pipeline:92 - --- Step 3: Direct HSI Pixel Georeferencing ---
2025-06-02 17:28:21 - main_pipeline - INFO - main_pipeline:96 - Step 3 completed successfully.
2025-06-02 17:28:21 - main_pipeline - INFO - main_pipeline:102 - --- Step 4: Georeferenced RGB GeoTIFF Creation ---
2025-06-02 17:28:21 - main_pipeline - INFO - main_pipeline:108 - Step 4 completed successfully.
2025-06-02 17:28:21 - main_pipeline - INFO - main_pipeline:114 - --- Step 5: Plot Generation ---
2025-06-02 17:28:21 - main_pipeline - INFO - main_pipeline:120 - Step 5 completed successfully.
2025-06-02 17:28:21 - main_pipeline - INFO - main_pipeline:126 - Complete HSI Georeferencing Pipeline executed successfully.
2025-06-02 17:28:21 - create_consolidated_webodm_poses - INFO - create_consolidated_webodm_poses:34 - Starting WebODM pose consolidation
2025-06-02 17:28:21 - create_consolidated_webodm_poses - INFO - create_consolidated_webodm_poses:52 - WebODM data directory: test_dir
2025-06-02 17:28:21 - create_consolidated_webodm_poses - INFO - create_consolidated_webodm_poses:53 - Shots GeoJSON file: test_dir\shots.geojson
2025-06-02 17:28:21 - create_consolidated_webodm_poses - INFO - create_consolidated_webodm_poses:54 - HAIP files directory: test_dir\haip
2025-06-02 17:28:21 - create_consolidated_webodm_poses - INFO - create_consolidated_webodm_poses:55 - Output CSV file: output\consolidated.csv
2025-06-02 17:28:21 - create_consolidated_webodm_poses - INFO - create_consolidated_webodm_poses:63 - Output directory created/verified: output
2025-06-02 17:28:21 - create_consolidated_webodm_poses - INFO - create_consolidated_webodm_poses:75 - Loading shots GeoJSON file: test_dir\shots.geojson
2025-06-02 17:28:21 - create_consolidated_webodm_poses - INFO - create_consolidated_webodm_poses:34 - Starting WebODM pose consolidation
2025-06-02 17:28:21 - create_consolidated_webodm_poses - INFO - create_consolidated_webodm_poses:34 - Starting WebODM pose consolidation
2025-06-02 17:28:21 - create_consolidated_webodm_poses - INFO - create_consolidated_webodm_poses:52 - WebODM data directory: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-87\test_missing_shots_file_raises0
2025-06-02 17:28:21 - create_consolidated_webodm_poses - INFO - create_consolidated_webodm_poses:53 - Shots GeoJSON file: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-87\test_missing_shots_file_raises0\nonexistent_shots.geojson
2025-06-02 17:28:21 - create_consolidated_webodm_poses - INFO - create_consolidated_webodm_poses:54 - HAIP files directory: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-87\test_missing_shots_file_raises0\haip
2025-06-02 17:28:21 - create_consolidated_webodm_poses - INFO - create_consolidated_webodm_poses:55 - Output CSV file: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-87\test_missing_shots_file_raises0\output\consolidated.csv
2025-06-02 17:28:21 - create_consolidated_webodm_poses - INFO - create_consolidated_webodm_poses:63 - Output directory created/verified: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-87\test_missing_shots_file_raises0\output
2025-06-02 17:28:21 - create_consolidated_webodm_poses - INFO - create_consolidated_webodm_poses:75 - Loading shots GeoJSON file: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-87\test_missing_shots_file_raises0\nonexistent_shots.geojson
2025-06-02 17:28:21 - create_consolidated_webodm_poses - INFO - create_consolidated_webodm_poses:34 - Starting WebODM pose consolidation
2025-06-02 17:28:21 - create_consolidated_webodm_poses - INFO - create_consolidated_webodm_poses:52 - WebODM data directory: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-87\test_invalid_json_raises_webod0
2025-06-02 17:28:21 - create_consolidated_webodm_poses - INFO - create_consolidated_webodm_poses:53 - Shots GeoJSON file: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-87\test_invalid_json_raises_webod0\shots.geojson
2025-06-02 17:28:21 - create_consolidated_webodm_poses - INFO - create_consolidated_webodm_poses:54 - HAIP files directory: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-87\test_invalid_json_raises_webod0\haip
2025-06-02 17:28:21 - create_consolidated_webodm_poses - INFO - create_consolidated_webodm_poses:55 - Output CSV file: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-87\test_invalid_json_raises_webod0\output\consolidated.csv
2025-06-02 17:28:21 - create_consolidated_webodm_poses - INFO - create_consolidated_webodm_poses:63 - Output directory created/verified: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-87\test_invalid_json_raises_webod0\output
2025-06-02 17:28:21 - create_consolidated_webodm_poses - INFO - create_consolidated_webodm_poses:75 - Loading shots GeoJSON file: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-87\test_invalid_json_raises_webod0\shots.geojson
2025-06-02 17:28:21 - create_consolidated_webodm_poses - INFO - create_consolidated_webodm_poses:34 - Starting WebODM pose consolidation
2025-06-02 17:28:21 - create_consolidated_webodm_poses - INFO - create_consolidated_webodm_poses:52 - WebODM data directory: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-87\test_missing_features_key_rais0
2025-06-02 17:28:21 - create_consolidated_webodm_poses - INFO - create_consolidated_webodm_poses:53 - Shots GeoJSON file: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-87\test_missing_features_key_rais0\shots.geojson
2025-06-02 17:28:21 - create_consolidated_webodm_poses - INFO - create_consolidated_webodm_poses:54 - HAIP files directory: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-87\test_missing_features_key_rais0\haip
2025-06-02 17:28:21 - create_consolidated_webodm_poses - INFO - create_consolidated_webodm_poses:55 - Output CSV file: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-87\test_missing_features_key_rais0\output\consolidated.csv
2025-06-02 17:28:21 - create_consolidated_webodm_poses - INFO - create_consolidated_webodm_poses:63 - Output directory created/verified: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-87\test_missing_features_key_rais0\output
2025-06-02 17:28:21 - create_consolidated_webodm_poses - INFO - create_consolidated_webodm_poses:75 - Loading shots GeoJSON file: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-87\test_missing_features_key_rais0\shots.geojson
2025-06-02 17:28:21 - create_consolidated_webodm_poses - INFO - create_consolidated_webodm_poses:34 - Starting WebODM pose consolidation
2025-06-02 17:28:21 - create_consolidated_webodm_poses - INFO - create_consolidated_webodm_poses:52 - WebODM data directory: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-87\test_successful_consolidation_0
2025-06-02 17:28:21 - create_consolidated_webodm_poses - INFO - create_consolidated_webodm_poses:53 - Shots GeoJSON file: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-87\test_successful_consolidation_0\shots.geojson
2025-06-02 17:28:21 - create_consolidated_webodm_poses - INFO - create_consolidated_webodm_poses:54 - HAIP files directory: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-87\test_successful_consolidation_0\haip
2025-06-02 17:28:21 - create_consolidated_webodm_poses - INFO - create_consolidated_webodm_poses:55 - Output CSV file: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-87\test_successful_consolidation_0\output\consolidated.csv
2025-06-02 17:28:21 - create_consolidated_webodm_poses - INFO - create_consolidated_webodm_poses:63 - Output directory created/verified: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-87\test_successful_consolidation_0\output
2025-06-02 17:28:21 - create_consolidated_webodm_poses - INFO - create_consolidated_webodm_poses:75 - Loading shots GeoJSON file: C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-87\test_successful_consolidation_0\shots.geojson
2025-06-02 17:28:21 - create_consolidated_webodm_poses - INFO - create_consolidated_webodm_poses:89 - Found 1 features in shots GeoJSON
2025-06-02 17:28:21 - create_consolidated_webodm_poses - INFO - create_consolidated_webodm_poses:188 - Processing complete: 1 entries processed, 0 entries skipped
2025-06-02 17:28:21 - create_consolidated_webodm_poses - INFO - create_consolidated_webodm_poses:204 - Successfully created C:\Users\<USER>\AppData\Local\Temp\pytest-of-Michel\pytest-87\test_successful_consolidation_0\output\consolidated.csv with 1 entries
2025-06-02 17:28:21 - synchronize_hsi_webodm - INFO - synchronize_hsi_webodm:356 - Starting HSI-WebODM synchronization
2025-06-02 17:28:21 - synchronize_hsi_webodm - INFO - synchronize_hsi_webodm:373 - HSI data directory: test_dir
2025-06-02 17:28:21 - synchronize_hsi_webodm - INFO - synchronize_hsi_webodm:374 - HSI sync file: test_dir\test_hsi_sync.txt
2025-06-02 17:28:21 - synchronize_hsi_webodm - INFO - synchronize_hsi_webodm:375 - HSI header file: test_dir\test_hsi.hdr
2025-06-02 17:28:21 - synchronize_hsi_webodm - INFO - synchronize_hsi_webodm:376 - WebODM CSV file: output\webodm.csv
2025-06-02 17:28:21 - synchronize_hsi_webodm - INFO - synchronize_hsi_webodm:377 - Output CSV file: output\hsi_poses.csv
2025-06-02 17:28:21 - synchronize_hsi_webodm - INFO - synchronize_hsi_webodm:385 - Output directory created/verified: output
2025-06-02 17:28:21 - synchronize_hsi_webodm - INFO - synchronize_hsi_webodm:390 - Loading HSI header file: test_dir\test_hsi.hdr
2025-06-02 17:28:21 - synchronize_hsi_webodm - ERROR - synchronize_hsi_webodm:407 - Error loading HSI metadata: Test
2025-06-02 17:28:21 - synchronize_hsi_webodm - INFO - synchronize_hsi_webodm:356 - Starting HSI-WebODM synchronization
2025-06-02 17:28:21 - synchronize_hsi_webodm - INFO - synchronize_hsi_webodm:202 - Loaded 2 WebODM pose records from CSV
2025-06-02 17:28:21 - synchronize_hsi_webodm - INFO - synchronize_hsi_webodm:229 - Successfully processed 2 WebODM poses
2025-06-02 17:28:21 - georeference_hsi_pixels - INFO - georeference_hsi_pixels:324 - Starting HSI pixel georeferencing
2025-06-02 17:28:21 - georeference_hsi_pixels - INFO - georeference_hsi_pixels:326 - Reading path configuration...
2025-06-02 17:28:21 - georeference_hsi_pixels - INFO - georeference_hsi_pixels:337 - Output directory: output (created if not exists)
2025-06-02 17:28:21 - georeference_hsi_pixels - INFO - georeference_hsi_pixels:341 - HSI poses CSV (expected in output_dir): output\poses.csv
2025-06-02 17:28:21 - georeference_hsi_pixels - INFO - georeference_hsi_pixels:345 - Output georeferenced pixels CSV: output\pixels.csv
2025-06-02 17:28:21 - georeference_hsi_pixels - INFO - georeference_hsi_pixels:350 - Reading georeferencing parameters...
2025-06-02 17:28:21 - georeference_hsi_pixels - INFO - georeference_hsi_pixels:355 - Boresight angles (degrees): Roll=0.0, Pitch=0.0, Yaw=0.0
2025-06-02 17:28:21 - georeference_hsi_pixels - INFO - georeference_hsi_pixels:371 - Sensor model correction: scale_vinkel_x=1.0, offset_vinkel_x=0.0
2025-06-02 17:28:21 - georeference_hsi_pixels - INFO - georeference_hsi_pixels:379 - Z_ground method: fixed_value, Offset: 20.0, Fixed value: 100.0
2025-06-02 17:28:21 - georeference_hsi_pixels - INFO - georeference_hsi_pixels:441 - Reading HSI header file: test_dir\test_hsi.hdr
2025-06-02 17:28:21 - georeference_hsi_pixels - INFO - georeference_hsi_pixels:324 - Starting HSI pixel georeferencing
2025-06-02 17:28:21 - georeference_hsi_pixels - INFO - georeference_hsi_pixels:326 - Reading path configuration...
2025-06-02 17:28:21 - georeference_hsi_pixels - WARNING - georeference_hsi_pixels:101 - No valid 'lever arm' or 'OffsetBetweenMainAntennaAndTargetPoint' entry found in header file
2025-06-02 17:28:21 - georeference_hsi_pixels - INFO - georeference_hsi_pixels:164 - Interpreting 'vinkelx_deg' and 'vinkely_deg' columns from sensor model as RADIANS directly
2025-06-02 17:28:22 - georeference_hsi_pixels - WARNING - georeference_hsi_pixels:145 - Number of entries in sensor model (2) does not match 'samples' (5)
2025-06-02 17:28:22 - georeference_hsi_pixels - WARNING - georeference_hsi_pixels:145 - Number of entries in sensor model (7) does not match 'samples' (5)
2025-06-02 17:28:22 - georeference_hsi_pixels - INFO - georeference_hsi_pixels:164 - Interpreting 'vinkelx_deg' and 'vinkely_deg' columns from sensor model as RADIANS directly
2025-06-02 17:28:22 - georeference_hsi_pixels - INFO - georeference_hsi_pixels:324 - Starting HSI pixel georeferencing
2025-06-02 17:28:22 - georeference_hsi_pixels - INFO - georeference_hsi_pixels:326 - Reading path configuration...
2025-06-02 17:28:22 - georeference_hsi_pixels - INFO - georeference_hsi_pixels:337 - Output directory: output (created if not exists)
2025-06-02 17:28:22 - georeference_hsi_pixels - INFO - georeference_hsi_pixels:341 - HSI poses CSV (expected in output_dir): output\poses.csv
2025-06-02 17:28:22 - georeference_hsi_pixels - INFO - georeference_hsi_pixels:345 - Output georeferenced pixels CSV: output\pixels.csv
2025-06-02 17:28:22 - georeference_hsi_pixels - INFO - georeference_hsi_pixels:350 - Reading georeferencing parameters...
2025-06-02 17:28:22 - georeference_hsi_pixels - INFO - georeference_hsi_pixels:355 - Boresight angles (degrees): Roll=0.0, Pitch=0.0, Yaw=0.0
2025-06-02 17:28:22 - georeference_hsi_pixels - INFO - georeference_hsi_pixels:371 - Sensor model correction: scale_vinkel_x=1.0, offset_vinkel_x=0.0
2025-06-02 17:28:22 - georeference_hsi_pixels - INFO - georeference_hsi_pixels:379 - Z_ground method: fixed_value, Offset: 20.0, Fixed value: 100.0
2025-06-02 17:28:22 - georeference_hsi_pixels - INFO - georeference_hsi_pixels:441 - Reading HSI header file: test_dir\test_hsi.hdr
2025-06-02 17:28:22 - georeference_hsi_pixels - INFO - georeference_hsi_pixels:447 - Header info: Samples: 640, Lines: 100, Lever arm from HDR: [0. 0. 0.]
2025-06-02 17:28:22 - lever_arm_utils - WARNING - lever_arm_utils:81 - Both HSI header and configuration provide zero lever arm values. Using (0,0,0) lever arm. Georeferencing accuracy may be compromised if the actual lever arm is non-zero.
2025-06-02 17:28:22 - georeference_hsi_pixels - INFO - georeference_hsi_pixels:458 - Effective lever arm (m) to be used in calculations: [0. 0. 0.]
2025-06-02 17:28:22 - georeference_hsi_pixels - INFO - georeference_hsi_pixels:462 - Using lever arm from HSI header
2025-06-02 17:28:22 - georeference_hsi_pixels - INFO - georeference_hsi_pixels:468 - Reading sensor angle model: test_dir\sensor.txt
2025-06-02 17:28:22 - georeference_hsi_pixels - INFO - georeference_hsi_pixels:474 - Sensor angles for 640 pixels loaded
2025-06-02 17:28:22 - georeference_hsi_pixels - INFO - georeference_hsi_pixels:475 - Original vinkelx_rad (treated as rad) range: min=0.000 rad, max=0.000 rad
2025-06-02 17:28:22 - georeference_hsi_pixels - INFO - georeference_hsi_pixels:476 - Equivalent degrees: min=0.00 deg, max=0.00 deg
2025-06-02 17:28:22 - georeference_hsi_pixels - INFO - georeference_hsi_pixels:478 - Reading poses file: output\poses.csv
2025-06-02 17:28:22 - georeference_hsi_pixels - INFO - georeference_hsi_pixels:485 - 100 poses loaded
2025-06-02 17:28:22 - georeference_hsi_pixels - INFO - georeference_hsi_pixels:511 - Z_ground (flat plane) method: 'fixed_value'
2025-06-02 17:28:22 - georeference_hsi_pixels - INFO - georeference_hsi_pixels:524 - Using Z_ground_flat_plane for projection (non-DSM methods): 100.00 meters
2025-06-02 17:28:22 - georeference_hsi_pixels - INFO - georeference_hsi_pixels:530 - Boresight angles (degrees) used: Roll=0.0, Pitch=0.0, Yaw=0.0
2025-06-02 17:28:22 - georeference_hsi_pixels - INFO - georeference_hsi_pixels:535 - Debugging output enabled for lines: {0, 50, 99}, pixels: {0, 320, 639}
2025-06-02 17:28:22 - georeference_hsi_pixels - INFO - georeference_hsi_pixels:544 - Starting main georeferencing processing loop...
2025-06-02 17:28:22 - georeference_hsi_pixels - INFO - georeference_hsi_pixels:548 - Processing HSI line 1/100...
2025-06-02 17:28:22 - georeference_hsi_pixels - INFO - georeference_hsi_pixels:694 - Processing completed. 0 of 64000 pixels could not be projected to ground (NaN)
2025-06-02 17:28:22 - georeference_hsi_pixels - WARNING - georeference_hsi_pixels:701 - Column 'yaw_deg' not found in poses DataFrame. Mean yaw cannot be calculated
2025-06-02 17:28:22 - georeference_hsi_pixels - INFO - georeference_hsi_pixels:703 - Saving georeferenced pixel coordinates to: output\pixels.csv
2025-06-02 17:28:22 - georeference_hsi_pixels - INFO - georeference_hsi_pixels:706 - File successfully saved
2025-06-02 17:28:22 - georeference_hsi_pixels - INFO - georeference_hsi_pixels:324 - Starting HSI pixel georeferencing
2025-06-02 17:28:22 - georeference_hsi_pixels - INFO - georeference_hsi_pixels:326 - Reading path configuration...
2025-06-02 17:28:22 - georeference_hsi_pixels - INFO - georeference_hsi_pixels:337 - Output directory: output (created if not exists)
2025-06-02 17:28:22 - georeference_hsi_pixels - INFO - georeference_hsi_pixels:341 - HSI poses CSV (expected in output_dir): output\poses.csv
2025-06-02 17:28:22 - georeference_hsi_pixels - INFO - georeference_hsi_pixels:345 - Output georeferenced pixels CSV: output\pixels.csv
2025-06-02 17:28:22 - georeference_hsi_pixels - INFO - georeference_hsi_pixels:350 - Reading georeferencing parameters...
2025-06-02 17:28:22 - georeference_hsi_pixels - INFO - georeference_hsi_pixels:355 - Boresight angles (degrees): Roll=0.0, Pitch=0.0, Yaw=0.0
2025-06-02 17:28:22 - georeference_hsi_pixels - INFO - georeference_hsi_pixels:371 - Sensor model correction: scale_vinkel_x=1.0, offset_vinkel_x=0.0
2025-06-02 17:28:22 - georeference_hsi_pixels - INFO - georeference_hsi_pixels:379 - Z_ground method: fixed_value, Offset: 20.0, Fixed value: 100.0
2025-06-02 17:28:22 - georeference_hsi_pixels - INFO - georeference_hsi_pixels:441 - Reading HSI header file: test_dir\test_hsi.hdr
2025-06-02 17:28:22 - georeference_hsi_pixels - INFO - georeference_hsi_pixels:160 - Detected angle values > 2π, interpreting as DEGREES and converting to radians
2025-06-02 17:28:22 - georeference_hsi_pixels - INFO - georeference_hsi_pixels:164 - Interpreting 'vinkelx_deg' and 'vinkely_deg' columns from sensor model as RADIANS directly
2025-06-02 17:28:22 - georeference_hsi_pixels - INFO - georeference_hsi_pixels:324 - Starting HSI pixel georeferencing
2025-06-02 17:28:22 - georeference_hsi_pixels - INFO - georeference_hsi_pixels:326 - Reading path configuration...
2025-06-02 17:28:22 - georeference_hsi_pixels - INFO - georeference_hsi_pixels:337 - Output directory: output (created if not exists)
2025-06-02 17:28:22 - georeference_hsi_pixels - INFO - georeference_hsi_pixels:341 - HSI poses CSV (expected in output_dir): output\poses.csv
2025-06-02 17:28:22 - georeference_hsi_pixels - INFO - georeference_hsi_pixels:345 - Output georeferenced pixels CSV: output\pixels.csv
2025-06-02 17:28:22 - georeference_hsi_pixels - INFO - georeference_hsi_pixels:350 - Reading georeferencing parameters...
2025-06-02 17:28:22 - georeference_hsi_pixels - INFO - georeference_hsi_pixels:355 - Boresight angles (degrees): Roll=0.0, Pitch=0.0, Yaw=0.0
2025-06-02 17:28:22 - georeference_hsi_pixels - INFO - georeference_hsi_pixels:371 - Sensor model correction: scale_vinkel_x=1.0, offset_vinkel_x=0.0
2025-06-02 17:28:22 - georeference_hsi_pixels - INFO - georeference_hsi_pixels:379 - Z_ground method: fixed_value, Offset: 20.0, Fixed value: 100.0
2025-06-02 17:28:22 - georeference_hsi_pixels - INFO - georeference_hsi_pixels:441 - Reading HSI header file: test_dir\test_hsi.hdr
2025-06-02 17:28:22 - georeference_hsi_pixels - INFO - georeference_hsi_pixels:447 - Header info: Samples: 2, Lines: 1, Lever arm from HDR: [0. 0. 0.]
2025-06-02 17:28:22 - lever_arm_utils - WARNING - lever_arm_utils:81 - Both HSI header and configuration provide zero lever arm values. Using (0,0,0) lever arm. Georeferencing accuracy may be compromised if the actual lever arm is non-zero.
2025-06-02 17:28:22 - georeference_hsi_pixels - INFO - georeference_hsi_pixels:458 - Effective lever arm (m) to be used in calculations: [0. 0. 0.]
2025-06-02 17:28:22 - georeference_hsi_pixels - INFO - georeference_hsi_pixels:462 - Using lever arm from HSI header
2025-06-02 17:28:22 - georeference_hsi_pixels - INFO - georeference_hsi_pixels:468 - Reading sensor angle model: test_dir\sensor.txt
2025-06-02 17:28:22 - georeference_hsi_pixels - INFO - georeference_hsi_pixels:474 - Sensor angles for 2 pixels loaded
2025-06-02 17:28:22 - georeference_hsi_pixels - INFO - georeference_hsi_pixels:475 - Original vinkelx_rad (treated as rad) range: min=0.000 rad, max=0.000 rad
2025-06-02 17:28:22 - georeference_hsi_pixels - INFO - georeference_hsi_pixels:476 - Equivalent degrees: min=0.00 deg, max=0.00 deg
2025-06-02 17:28:22 - georeference_hsi_pixels - INFO - georeference_hsi_pixels:478 - Reading poses file: output\poses.csv
2025-06-02 17:28:22 - georeference_hsi_pixels - INFO - georeference_hsi_pixels:485 - 1 poses loaded
2025-06-02 17:28:22 - georeference_hsi_pixels - INFO - georeference_hsi_pixels:511 - Z_ground (flat plane) method: 'fixed_value'
2025-06-02 17:28:22 - georeference_hsi_pixels - INFO - georeference_hsi_pixels:524 - Using Z_ground_flat_plane for projection (non-DSM methods): 100.00 meters
2025-06-02 17:28:22 - georeference_hsi_pixels - INFO - georeference_hsi_pixels:530 - Boresight angles (degrees) used: Roll=0.0, Pitch=0.0, Yaw=0.0
2025-06-02 17:28:22 - georeference_hsi_pixels - INFO - georeference_hsi_pixels:535 - Debugging output enabled for lines: {0}, pixels: {0, 1}
2025-06-02 17:28:22 - georeference_hsi_pixels - INFO - georeference_hsi_pixels:544 - Starting main georeferencing processing loop...
2025-06-02 17:28:22 - georeference_hsi_pixels - INFO - georeference_hsi_pixels:548 - Processing HSI line 1/1...
2025-06-02 17:28:22 - georeference_hsi_pixels - WARNING - georeference_hsi_pixels:614 - Vectorized processing failed for line 0 due to VectorizedProcessingError: Invalid quaternion for vectorized processing. Falling back to individual pixel processing
2025-06-02 17:28:22 - georeference_hsi_pixels - INFO - georeference_hsi_pixels:694 - Processing completed. 0 of 2 pixels could not be projected to ground (NaN)
2025-06-02 17:28:22 - georeference_hsi_pixels - WARNING - georeference_hsi_pixels:701 - Column 'yaw_deg' not found in poses DataFrame. Mean yaw cannot be calculated
2025-06-02 17:28:22 - georeference_hsi_pixels - INFO - georeference_hsi_pixels:703 - Saving georeferenced pixel coordinates to: output\pixels.csv
2025-06-02 17:28:22 - georeference_hsi_pixels - INFO - georeference_hsi_pixels:706 - File successfully saved
