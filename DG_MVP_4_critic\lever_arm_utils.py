"""
Utility functions for lever arm handling in the HSI Georeferencing Pipeline.

This module implements the logic for determining the effective lever arm
to use in georeferencing calculations, prioritizing HSI header data with
configuration overrides.
"""

import numpy as np
import logging
from typing import Tuple, Optional

logger = logging.getLogger(__name__)


def determine_effective_lever_arm(
    lever_arm_from_hdr: Optional[np.ndarray],
    config_lever_arm: Optional[np.ndarray]
) -> Tuple[np.ndarray, bool]:
    """
    Determine the effective lever arm to use for georeferencing calculations.
    
    Priority logic:
    1. Use config lever arm if it's explicitly set and non-zero
    2. Otherwise, use lever arm from HSI header if available and non-zero
    3. Fall back to zero lever arm with warning
    
    Args:
        lever_arm_from_hdr: Lever arm parsed from HSI header file (x, y, z in meters)
        config_lever_arm: Lever arm from configuration file (x, y, z in meters)
        
    Returns:
        Tuple of (effective_lever_arm, config_override_used)
        - effective_lever_arm: The lever arm to use (3-element numpy array)
        - config_override_used: True if config override was used, False otherwise
    """
    
    # Convert inputs to numpy arrays if they aren't already
    if lever_arm_from_hdr is not None:
        lever_arm_from_hdr = np.asarray(lever_arm_from_hdr)
    if config_lever_arm is not None:
        config_lever_arm = np.asarray(config_lever_arm)
    
    # Check if config lever arm is explicitly set and non-zero
    config_is_nonzero = (
        config_lever_arm is not None and 
        not np.allclose(config_lever_arm, 0.0, atol=1e-9)
    )
    
    # Check if HDR lever arm is available and non-zero
    hdr_is_nonzero = (
        lever_arm_from_hdr is not None and 
        not np.allclose(lever_arm_from_hdr, 0.0, atol=1e-9)
    )
    
    if config_is_nonzero:
        # Use config override
        effective_lever_arm = config_lever_arm.copy()
        config_override_used = True
        logger.info(f"Using lever arm from configuration override: {effective_lever_arm}")
        
    elif hdr_is_nonzero:
        # Use HDR lever arm
        effective_lever_arm = lever_arm_from_hdr.copy()
        config_override_used = False
        logger.info(f"Using lever arm from HSI header: {effective_lever_arm}")
        
    else:
        # Fall back to zero lever arm
        effective_lever_arm = np.array([0.0, 0.0, 0.0])
        config_override_used = False
        
        # Generate appropriate warnings
        if hdr_is_nonzero and not config_is_nonzero:
            logger.warning(
                f"Effective lever arm is (0,0,0) but HSI header reported a non-zero "
                f"lever arm {lever_arm_from_hdr} which was not overridden by a "
                f"non-zero configuration. Georeferencing accuracy may be compromised."
            )
        elif not hdr_is_nonzero and not config_is_nonzero:
            logger.warning(
                "Both HSI header and configuration provide zero lever arm values. "
                "Using (0,0,0) lever arm. Georeferencing accuracy may be compromised "
                "if the actual lever arm is non-zero."
            )
    
    return effective_lever_arm, config_override_used


def validate_lever_arm(lever_arm: np.ndarray) -> bool:
    """
    Validate that a lever arm array is reasonable.
    
    Args:
        lever_arm: 3-element array representing lever arm in meters
        
    Returns:
        True if lever arm appears valid, False otherwise
    """
    if lever_arm is None:
        return False
        
    lever_arm = np.asarray(lever_arm)
    
    # Check shape
    if lever_arm.shape != (3,):
        logger.error(f"Lever arm must be 3-element array, got shape {lever_arm.shape}")
        return False
    
    # Check for NaN or infinite values
    if not np.all(np.isfinite(lever_arm)):
        logger.error(f"Lever arm contains NaN or infinite values: {lever_arm}")
        return False
    
    # Check for reasonable magnitude (less than 10 meters in any direction)
    if np.any(np.abs(lever_arm) > 10.0):
        logger.warning(f"Lever arm has unusually large magnitude: {lever_arm}")
        # Don't return False, just warn - large lever arms might be valid
    
    return True
