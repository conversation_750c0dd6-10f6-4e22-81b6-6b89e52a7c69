[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "dg-mvp"
version = "0.1.0"
description = "HSI Direct Georeferencing Pipeline"
requires-python = ">=3.10"
dependencies = [
    "black>=25.1.0",
    "matplotlib>=3.10.3",
    "numba>=0.61.2",
    "numpy>=2.2.6",
    "opencv-python>=*********",
    "pandas>=2.2.3",
    "rasterio>=1.4.3",
    "scipy>=1.15.3",
    "spectral>=0.24",
    "toml>=0.10.2",
]

[tool.setuptools.packages.find]
where = ["."]
include = ["*.py"]
exclude = ["data*", "plots*", "research*", "test_*"]

[project.optional-dependencies]
test = [
    "pytest>=7.0.0",
    "pytest-cov>=4.0.0",
    "pytest-benchmark>=4.0.0",
    "pytest-mock>=3.10.0",
]

[tool.pytest.ini_options]
testpaths = [".", "tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = [
    "--strict-markers",
    "--strict-config",
    "--verbose",
    "--cov=.",
    "--cov-report=term-missing",
    "--cov-report=html:htmlcov",
    "--cov-report=xml",
]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
]

[tool.coverage.run]
source = ["."]
omit = [
    "test_*.py",
    "*_test.py",
    "tests/*",
    "setup.py",
    "*/site-packages/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]
