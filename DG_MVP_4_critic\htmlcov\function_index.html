<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_718ce007.css" type="text/css">
    <script src="coverage_html_cb_497bf287.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">36%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>n</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button" href="index.html">Files</a>
                <a class="button current">Functions</a>
                <a class="button" href="class_index.html">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.8.2">coverage.py v7.8.2</a>,
            created at 2025-06-02 16:33 +0200
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="region" class="name left" aria-sort="none" data-default-sort-order="ascending" data-shortcut="n">function<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="compare_timestamps_py.html#t5">compare_timestamps.py</a></td>
                <td class="name left"><a href="compare_timestamps_py.html#t5"><data value='parse_haip_timestamp'>parse_haip_timestamp</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="compare_timestamps_py.html#t16">compare_timestamps.py</a></td>
                <td class="name left"><a href="compare_timestamps_py.html#t16"><data value='main'>main</data></a></td>
                <td>43</td>
                <td>43</td>
                <td>0</td>
                <td class="right" data-ratio="0 43">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="compare_timestamps_py.html">compare_timestamps.py</a></td>
                <td class="name left"><a href="compare_timestamps_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>5</td>
                <td>5</td>
                <td>2</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="create_consolidated_webodm_poses_py.html#t18">create_consolidated_webodm_poses.py</a></td>
                <td class="name left"><a href="create_consolidated_webodm_poses_py.html#t18"><data value='run_consolidation'>run_consolidation</data></a></td>
                <td>105</td>
                <td>29</td>
                <td>0</td>
                <td class="right" data-ratio="76 105">72%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="create_consolidated_webodm_poses_py.html">create_consolidated_webodm_poses.py</a></td>
                <td class="name left"><a href="create_consolidated_webodm_poses_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>7</td>
                <td>0</td>
                <td>17</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="create_georeferenced_rgb_py.html#t14">create_georeferenced_rgb.py</a></td>
                <td class="name left"><a href="create_georeferenced_rgb_py.html#t14"><data value='find_nearest_band_index'>find_nearest_band_index</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="create_georeferenced_rgb_py.html#t20">create_georeferenced_rgb.py</a></td>
                <td class="name left"><a href="create_georeferenced_rgb_py.html#t20"><data value='normalize_band'>normalize_band</data></a></td>
                <td>23</td>
                <td>23</td>
                <td>0</td>
                <td class="right" data-ratio="0 23">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="create_georeferenced_rgb_py.html#t58">create_georeferenced_rgb.py</a></td>
                <td class="name left"><a href="create_georeferenced_rgb_py.html#t58"><data value='run_create_rgb_geotiff'>run_create_rgb_geotiff</data></a></td>
                <td>139</td>
                <td>139</td>
                <td>0</td>
                <td class="right" data-ratio="0 139">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="create_georeferenced_rgb_py.html">create_georeferenced_rgb.py</a></td>
                <td class="name left"><a href="create_georeferenced_rgb_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>13</td>
                <td>0</td>
                <td>12</td>
                <td class="right" data-ratio="13 13">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="georeference_hsi_pixels_py.html#t33">georeference_hsi_pixels.py</a></td>
                <td class="name left"><a href="georeference_hsi_pixels_py.html#t33"><data value='parse_hsi_header'>parse_hsi_header</data></a></td>
                <td>28</td>
                <td>28</td>
                <td>0</td>
                <td class="right" data-ratio="0 28">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="georeference_hsi_pixels_py.html#t78">georeference_hsi_pixels.py</a></td>
                <td class="name left"><a href="georeference_hsi_pixels_py.html#t78"><data value='parse_sensor_model'>parse_sensor_model</data></a></td>
                <td>20</td>
                <td>20</td>
                <td>0</td>
                <td class="right" data-ratio="0 20">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="georeference_hsi_pixels_py.html#t114">georeference_hsi_pixels.py</a></td>
                <td class="name left"><a href="georeference_hsi_pixels_py.html#t114"><data value='calculate_ray_dsm_intersection'>calculate_ray_dsm_intersection</data></a></td>
                <td>68</td>
                <td>68</td>
                <td>0</td>
                <td class="right" data-ratio="0 68">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="georeference_hsi_pixels_py.html#t119">georeference_hsi_pixels.py</a></td>
                <td class="name left"><a href="georeference_hsi_pixels_py.html#t119"><data value='get_dsm_z'>calculate_ray_dsm_intersection.get_dsm_z</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="georeference_hsi_pixels_py.html#t131">georeference_hsi_pixels.py</a></td>
                <td class="name left"><a href="georeference_hsi_pixels_py.html#t131"><data value='func_to_solve'>calculate_ray_dsm_intersection.func_to_solve</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="georeference_hsi_pixels_py.html#t248">georeference_hsi_pixels.py</a></td>
                <td class="name left"><a href="georeference_hsi_pixels_py.html#t248"><data value='run_georeferencing'>run_georeferencing</data></a></td>
                <td>249</td>
                <td>249</td>
                <td>0</td>
                <td class="right" data-ratio="0 249">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="georeference_hsi_pixels_py.html">georeference_hsi_pixels.py</a></td>
                <td class="name left"><a href="georeference_hsi_pixels_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>13</td>
                <td>0</td>
                <td>8</td>
                <td class="right" data-ratio="13 13">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="lever_arm_utils_py.html#t16">lever_arm_utils.py</a></td>
                <td class="name left"><a href="lever_arm_utils_py.html#t16"><data value='determine_effective_lever_arm'>determine_effective_lever_arm</data></a></td>
                <td>21</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="20 21">95%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="lever_arm_utils_py.html#t90">lever_arm_utils.py</a></td>
                <td class="name left"><a href="lever_arm_utils_py.html#t90"><data value='validate_lever_arm'>validate_lever_arm</data></a></td>
                <td>12</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="12 12">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="lever_arm_utils_py.html">lever_arm_utils.py</a></td>
                <td class="name left"><a href="lever_arm_utils_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="logging_config_py.html#t14">logging_config.py</a></td>
                <td class="name left"><a href="logging_config_py.html#t14"><data value='setup_logging'>setup_logging</data></a></td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="logging_config_py.html#t75">logging_config.py</a></td>
                <td class="name left"><a href="logging_config_py.html#t75"><data value='get_logger'>get_logger</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="logging_config_py.html">logging_config.py</a></td>
                <td class="name left"><a href="logging_config_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="main_pipeline_py.html#t17">main_pipeline.py</a></td>
                <td class="name left"><a href="main_pipeline_py.html#t17"><data value='load_pipeline_config'>load_pipeline_config</data></a></td>
                <td>13</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="13 13">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="main_pipeline_py.html#t49">main_pipeline.py</a></td>
                <td class="name left"><a href="main_pipeline_py.html#t49"><data value='run_complete_pipeline'>run_complete_pipeline</data></a></td>
                <td>58</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="44 58">76%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="main_pipeline_py.html">main_pipeline.py</a></td>
                <td class="name left"><a href="main_pipeline_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>13</td>
                <td>0</td>
                <td>6</td>
                <td class="right" data-ratio="13 13">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="pipeline_exceptions_py.html">pipeline_exceptions.py</a></td>
                <td class="name left"><a href="pipeline_exceptions_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>16</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="16 16">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="plot_hsi_data_py.html#t8">plot_hsi_data.py</a></td>
                <td class="name left"><a href="plot_hsi_data_py.html#t8"><data value='plot_positions'>plot_positions</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="plot_hsi_data_py.html#t24">plot_hsi_data.py</a></td>
                <td class="name left"><a href="plot_hsi_data_py.html#t24"><data value='plot_trajectory_2d'>plot_trajectory_2d</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="plot_hsi_data_py.html#t38">plot_hsi_data.py</a></td>
                <td class="name left"><a href="plot_hsi_data_py.html#t38"><data value='plot_orientations'>plot_orientations</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="plot_hsi_data_py.html#t55">plot_hsi_data.py</a></td>
                <td class="name left"><a href="plot_hsi_data_py.html#t55"><data value='plot_interpolation_quality'>plot_interpolation_quality</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="plot_hsi_data_py.html#t70">plot_hsi_data.py</a></td>
                <td class="name left"><a href="plot_hsi_data_py.html#t70"><data value='plot_yaw_and_flight_direction'>plot_yaw_and_flight_direction</data></a></td>
                <td>20</td>
                <td>20</td>
                <td>0</td>
                <td class="right" data-ratio="0 20">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="plot_hsi_data_py.html#t108">plot_hsi_data.py</a></td>
                <td class="name left"><a href="plot_hsi_data_py.html#t108"><data value='run_plotting'>run_plotting</data></a></td>
                <td>45</td>
                <td>45</td>
                <td>0</td>
                <td class="right" data-ratio="0 45">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="plot_hsi_data_py.html">plot_hsi_data.py</a></td>
                <td class="name left"><a href="plot_hsi_data_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>11</td>
                <td>0</td>
                <td>8</td>
                <td class="right" data-ratio="11 11">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="synchronize_hsi_webodm_py.html#t23">synchronize_hsi_webodm.py</a></td>
                <td class="name left"><a href="synchronize_hsi_webodm_py.html#t23"><data value='parse_hdr_file'>parse_hdr_file</data></a></td>
                <td>26</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="20 26">77%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="synchronize_hsi_webodm_py.html#t74">synchronize_hsi_webodm.py</a></td>
                <td class="name left"><a href="synchronize_hsi_webodm_py.html#t74"><data value='convert_hsi_timestamp_to_ns'>convert_hsi_timestamp_to_ns</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="synchronize_hsi_webodm_py.html#t87">synchronize_hsi_webodm.py</a></td>
                <td class="name left"><a href="synchronize_hsi_webodm_py.html#t87"><data value='load_hsi_data'>load_hsi_data</data></a></td>
                <td>34</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="26 34">76%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="synchronize_hsi_webodm_py.html#t167">synchronize_hsi_webodm.py</a></td>
                <td class="name left"><a href="synchronize_hsi_webodm_py.html#t167"><data value='load_webodm_data'>load_webodm_data</data></a></td>
                <td>30</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="22 30">73%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="synchronize_hsi_webodm_py.html#t240">synchronize_hsi_webodm.py</a></td>
                <td class="name left"><a href="synchronize_hsi_webodm_py.html#t240"><data value='interpolate_pose'>interpolate_pose</data></a></td>
                <td>44</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="37 44">84%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="synchronize_hsi_webodm_py.html#t337">synchronize_hsi_webodm.py</a></td>
                <td class="name left"><a href="synchronize_hsi_webodm_py.html#t337"><data value='run_synchronization'>run_synchronization</data></a></td>
                <td>82</td>
                <td>53</td>
                <td>0</td>
                <td class="right" data-ratio="29 82">35%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="synchronize_hsi_webodm_py.html">synchronize_hsi_webodm.py</a></td>
                <td class="name left"><a href="synchronize_hsi_webodm_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>15</td>
                <td>0</td>
                <td>17</td>
                <td class="right" data-ratio="15 15">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="vectorized_georef_py.html#t16">vectorized_georef.py</a></td>
                <td class="name left"><a href="vectorized_georef_py.html#t16"><data value='calculate_sensor_view_vectors_vectorized'>calculate_sensor_view_vectors_vectorized</data></a></td>
                <td>12</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="12 12">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="vectorized_georef_py.html#t63">vectorized_georef.py</a></td>
                <td class="name left"><a href="vectorized_georef_py.html#t63"><data value='transform_to_world_coordinates_vectorized'>transform_to_world_coordinates_vectorized</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="vectorized_georef_py.html#t84">vectorized_georef.py</a></td>
                <td class="name left"><a href="vectorized_georef_py.html#t84"><data value='calculate_flat_plane_intersections_vectorized'>calculate_flat_plane_intersections_vectorized</data></a></td>
                <td>15</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="15 15">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="vectorized_georef_py.html#t134">vectorized_georef.py</a></td>
                <td class="name left"><a href="vectorized_georef_py.html#t134"><data value='process_hsi_line_vectorized'>process_hsi_line_vectorized</data></a></td>
                <td>25</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="18 25">72%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="vectorized_georef_py.html">vectorized_georef.py</a></td>
                <td class="name left"><a href="vectorized_georef_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>9</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="9 9">100%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td class="name left">&nbsp;</td>
                <td>1302</td>
                <td>838</td>
                <td>70</td>
                <td class="right" data-ratio="464 1302">36%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.8.2">coverage.py v7.8.2</a>,
            created at 2025-06-02 16:33 +0200
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href=""></a>
        <a id="nextFileLink" class="nav" href=""></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>
