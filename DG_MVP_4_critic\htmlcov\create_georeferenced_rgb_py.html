<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage for create_georeferenced_rgb.py: 7%</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_718ce007.css" type="text/css">
    <script src="coverage_html_cb_497bf287.js" defer></script>
</head>
<body class="pyfile">
<header>
    <div class="content">
        <h1>
            <span class="text">Coverage for </span><b>create_georeferenced_rgb.py</b>:
            <span class="pc_cov">7%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>r</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        &nbsp; toggle line displays
                    </p>
                    <p>
                        <kbd>j</kbd>
                        <kbd>k</kbd>
                        &nbsp; next/prev highlighted chunk
                    </p>
                    <p>
                        <kbd>0</kbd> &nbsp; (zero) top of page
                    </p>
                    <p>
                        <kbd>1</kbd> &nbsp; (one) first highlighted chunk
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>u</kbd> &nbsp; up to the index
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <h2>
            <span class="text">178 statements &nbsp;</span>
            <button type="button" class="run button_toggle_run" value="run" data-shortcut="r" title="Toggle lines run">13<span class="text"> run</span></button>
            <button type="button" class="mis show_mis button_toggle_mis" value="mis" data-shortcut="m" title="Toggle lines missing">165<span class="text"> missing</span></button>
            <button type="button" class="exc show_exc button_toggle_exc" value="exc" data-shortcut="x" title="Toggle lines excluded">12<span class="text"> excluded</span></button>
        </h2>
        <p class="text">
            <a id="prevFileLink" class="nav" href="create_consolidated_webodm_poses_py.html">&#xab; prev</a> &nbsp; &nbsp;
            <a id="indexLink" class="nav" href="index.html">&Hat; index</a> &nbsp; &nbsp;
            <a id="nextFileLink" class="nav" href="georeference_hsi_pixels_py.html">&#xbb; next</a>
            &nbsp; &nbsp; &nbsp;
            <a class="nav" href="https://coverage.readthedocs.io/en/7.8.2">coverage.py v7.8.2</a>,
            created at 2025-06-02 16:11 +0200
        </p>
        <aside class="hidden">
            <button type="button" class="button_next_chunk" data-shortcut="j"></button>
            <button type="button" class="button_prev_chunk" data-shortcut="k"></button>
            <button type="button" class="button_top_of_page" data-shortcut="0"></button>
            <button type="button" class="button_first_chunk" data-shortcut="1"></button>
            <button type="button" class="button_prev_file" data-shortcut="["></button>
            <button type="button" class="button_next_file" data-shortcut="]"></button>
            <button type="button" class="button_to_index" data-shortcut="u"></button>
            <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
        </aside>
    </div>
</header>
<main id="source">
    <p class="run"><span class="n"><a id="t1" href="#t1">1</a></span><span class="t"><span class="key">import</span> <span class="nam">rasterio</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t2" href="#t2">2</a></span><span class="t"><span class="key">from</span> <span class="nam">rasterio</span><span class="op">.</span><span class="nam">transform</span> <span class="key">import</span> <span class="nam">Affine</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t3" href="#t3">3</a></span><span class="t"><span class="key">import</span> <span class="nam">numpy</span> <span class="key">as</span> <span class="nam">np</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t4" href="#t4">4</a></span><span class="t"><span class="key">import</span> <span class="nam">pandas</span> <span class="key">as</span> <span class="nam">pd</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t5" href="#t5">5</a></span><span class="t"><span class="key">from</span> <span class="nam">scipy</span><span class="op">.</span><span class="nam">spatial</span> <span class="key">import</span> <span class="nam">KDTree</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t6" href="#t6">6</a></span><span class="t"><span class="key">import</span> <span class="nam">spectral</span> <span class="com"># For reading .hdr and .img files</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t7" href="#t7">7</a></span><span class="t"><span class="key">import</span> <span class="nam">toml</span> <span class="com"># For reading config file</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t8" href="#t8">8</a></span><span class="t"><span class="key">import</span> <span class="nam">os</span>   <span class="com"># For path operations</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t9" href="#t9">9</a></span><span class="t"><span class="key">import</span> <span class="nam">datetime</span> <span class="com"># For timestamp</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t10" href="#t10">10</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t11" href="#t11">11</a></span><span class="t"><span class="com"># --- Global constants (can be moved inside function if preferred, but NO_DATA_VALUE is simple) ---</span>&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t12" href="#t12">12</a></span><span class="t"><span class="nam">NO_DATA_VALUE</span> <span class="op">=</span> <span class="num">0</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t13" href="#t13">13</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t14" href="#t14">14</a></span><span class="t"><span class="key">def</span> <span class="nam">find_nearest_band_index</span><span class="op">(</span><span class="nam">wavelengths_list</span><span class="op">,</span> <span class="nam">target_wavelength</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t15" href="#t15">15</a></span><span class="t">    <span class="str">"""Finds the index of the band closest to the target wavelength."""</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t16" href="#t16">16</a></span><span class="t">    <span class="nam">wavelengths_array</span> <span class="op">=</span> <span class="nam">np</span><span class="op">.</span><span class="nam">array</span><span class="op">(</span><span class="op">[</span><span class="nam">float</span><span class="op">(</span><span class="nam">w</span><span class="op">)</span> <span class="key">for</span> <span class="nam">w</span> <span class="key">in</span> <span class="nam">wavelengths_list</span><span class="op">]</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t17" href="#t17">17</a></span><span class="t">    <span class="nam">idx</span> <span class="op">=</span> <span class="op">(</span><span class="nam">np</span><span class="op">.</span><span class="nam">abs</span><span class="op">(</span><span class="nam">wavelengths_array</span> <span class="op">-</span> <span class="nam">target_wavelength</span><span class="op">)</span><span class="op">)</span><span class="op">.</span><span class="nam">argmin</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t18" href="#t18">18</a></span><span class="t">    <span class="key">return</span> <span class="nam">idx</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t19" href="#t19">19</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t20" href="#t20">20</a></span><span class="t"><span class="key">def</span> <span class="nam">normalize_band</span><span class="op">(</span><span class="nam">band_data</span><span class="op">,</span> <span class="nam">method</span><span class="op">=</span><span class="str">"min_max"</span><span class="op">,</span> <span class="nam">no_data_val</span><span class="op">=</span><span class="num">0</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t21" href="#t21">21</a></span><span class="t">    <span class="str">"""Normalizes a band to 0-255 (uint8) based on the specified method, excluding no_data_val."""</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t22" href="#t22">22</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t23" href="#t23">23</a></span><span class="t">    <span class="nam">is_no_data_mask</span> <span class="op">=</span> <span class="op">(</span><span class="nam">band_data</span> <span class="op">==</span> <span class="nam">no_data_val</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t24" href="#t24">24</a></span><span class="t">    <span class="nam">is_nan_mask</span> <span class="op">=</span> <span class="nam">np</span><span class="op">.</span><span class="nam">isnan</span><span class="op">(</span><span class="nam">band_data</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t25" href="#t25">25</a></span><span class="t">    <span class="nam">valid_data_mask</span> <span class="op">=</span> <span class="op">~</span><span class="nam">is_no_data_mask</span> <span class="op">&amp;</span> <span class="op">~</span><span class="nam">is_nan_mask</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t26" href="#t26">26</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t27" href="#t27">27</a></span><span class="t">    <span class="nam">valid_data</span> <span class="op">=</span> <span class="nam">band_data</span><span class="op">[</span><span class="nam">valid_data_mask</span><span class="op">]</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t28" href="#t28">28</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t29" href="#t29">29</a></span><span class="t">    <span class="key">if</span> <span class="nam">valid_data</span><span class="op">.</span><span class="nam">size</span> <span class="op">==</span> <span class="num">0</span><span class="op">:</span> <span class="com"># All data is no_data_val or NaN</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t30" href="#t30">30</a></span><span class="t">        <span class="key">return</span> <span class="nam">np</span><span class="op">.</span><span class="nam">full_like</span><span class="op">(</span><span class="nam">band_data</span><span class="op">,</span> <span class="nam">no_data_val</span><span class="op">,</span> <span class="nam">dtype</span><span class="op">=</span><span class="nam">np</span><span class="op">.</span><span class="nam">uint8</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t31" href="#t31">31</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t32" href="#t32">32</a></span><span class="t">    <span class="key">if</span> <span class="nam">method</span> <span class="op">==</span> <span class="str">"min_max"</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t33" href="#t33">33</a></span><span class="t">        <span class="nam">min_val</span> <span class="op">=</span> <span class="nam">np</span><span class="op">.</span><span class="nam">min</span><span class="op">(</span><span class="nam">valid_data</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t34" href="#t34">34</a></span><span class="t">        <span class="nam">max_val</span> <span class="op">=</span> <span class="nam">np</span><span class="op">.</span><span class="nam">max</span><span class="op">(</span><span class="nam">valid_data</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t35" href="#t35">35</a></span><span class="t">    <span class="key">elif</span> <span class="nam">method</span> <span class="op">==</span> <span class="str">"percentile_2_98"</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t36" href="#t36">36</a></span><span class="t">        <span class="nam">min_val</span> <span class="op">=</span> <span class="nam">np</span><span class="op">.</span><span class="nam">percentile</span><span class="op">(</span><span class="nam">valid_data</span><span class="op">,</span> <span class="num">2</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t37" href="#t37">37</a></span><span class="t">        <span class="nam">max_val</span> <span class="op">=</span> <span class="nam">np</span><span class="op">.</span><span class="nam">percentile</span><span class="op">(</span><span class="nam">valid_data</span><span class="op">,</span> <span class="num">98</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t38" href="#t38">38</a></span><span class="t">    <span class="key">else</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t39" href="#t39">39</a></span><span class="t">        <span class="key">raise</span> <span class="nam">ValueError</span><span class="op">(</span><span class="str">f"Unsupported normalization method '{method}'. Supported: 'min_max', 'percentile_2_98'."</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t40" href="#t40">40</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t41" href="#t41">41</a></span><span class="t">    <span class="nam">normalized_band_data</span> <span class="op">=</span> <span class="nam">np</span><span class="op">.</span><span class="nam">zeros_like</span><span class="op">(</span><span class="nam">band_data</span><span class="op">,</span> <span class="nam">dtype</span><span class="op">=</span><span class="nam">np</span><span class="op">.</span><span class="nam">float32</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t42" href="#t42">42</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t43" href="#t43">43</a></span><span class="t">    <span class="key">if</span> <span class="nam">np</span><span class="op">.</span><span class="nam">isclose</span><span class="op">(</span><span class="nam">max_val</span><span class="op">,</span> <span class="nam">min_val</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t44" href="#t44">44</a></span><span class="t">        <span class="nam">normalized_band_data</span><span class="op">[</span><span class="nam">valid_data_mask</span><span class="op">]</span> <span class="op">=</span> <span class="num">0.0</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t45" href="#t45">45</a></span><span class="t">    <span class="key">else</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t46" href="#t46">46</a></span><span class="t">        <span class="nam">normalized_values</span> <span class="op">=</span> <span class="num">255.0</span> <span class="op">*</span> <span class="op">(</span><span class="nam">valid_data</span> <span class="op">-</span> <span class="nam">min_val</span><span class="op">)</span> <span class="op">/</span> <span class="op">(</span><span class="nam">max_val</span> <span class="op">-</span> <span class="nam">min_val</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t47" href="#t47">47</a></span><span class="t">        <span class="nam">normalized_band_data</span><span class="op">[</span><span class="nam">valid_data_mask</span><span class="op">]</span> <span class="op">=</span> <span class="nam">normalized_values</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t48" href="#t48">48</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t49" href="#t49">49</a></span><span class="t">    <span class="nam">normalized_band_data</span> <span class="op">=</span> <span class="nam">np</span><span class="op">.</span><span class="nam">clip</span><span class="op">(</span><span class="nam">normalized_band_data</span><span class="op">,</span> <span class="num">0</span><span class="op">,</span> <span class="num">255</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t50" href="#t50">50</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t51" href="#t51">51</a></span><span class="t">    <span class="nam">output_uint8</span> <span class="op">=</span> <span class="nam">normalized_band_data</span><span class="op">.</span><span class="nam">astype</span><span class="op">(</span><span class="nam">np</span><span class="op">.</span><span class="nam">uint8</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t52" href="#t52">52</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t53" href="#t53">53</a></span><span class="t">    <span class="nam">output_uint8</span><span class="op">[</span><span class="nam">is_no_data_mask</span><span class="op">]</span> <span class="op">=</span> <span class="nam">np</span><span class="op">.</span><span class="nam">uint8</span><span class="op">(</span><span class="nam">no_data_val</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t54" href="#t54">54</a></span><span class="t">    <span class="nam">output_uint8</span><span class="op">[</span><span class="nam">is_nan_mask</span><span class="op">]</span> <span class="op">=</span> <span class="nam">np</span><span class="op">.</span><span class="nam">uint8</span><span class="op">(</span><span class="nam">no_data_val</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t55" href="#t55">55</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t56" href="#t56">56</a></span><span class="t">    <span class="key">return</span> <span class="nam">output_uint8</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t57" href="#t57">57</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="run"><span class="n"><a id="t58" href="#t58">58</a></span><span class="t"><span class="key">def</span> <span class="nam">run_create_rgb_geotiff</span><span class="op">(</span><span class="nam">config_path</span><span class="op">:</span> <span class="nam">str</span><span class="op">)</span> <span class="op">-></span> <span class="nam">bool</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t59" href="#t59">59</a></span><span class="t">    <span class="str">"""</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t60" href="#t60">60</a></span><span class="t"><span class="str">    Creates a georeferenced RGB GeoTIFF from HSI data based on a configuration file.</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t61" href="#t61">61</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t62" href="#t62">62</a></span><span class="t"><span class="str">    Args:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t63" href="#t63">63</a></span><span class="t"><span class="str">        config_path: Path to the TOML configuration file.</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t64" href="#t64">64</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t65" href="#t65">65</a></span><span class="t"><span class="str">    Returns:</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t66" href="#t66">66</a></span><span class="t"><span class="str">        True if the GeoTIFF was created successfully, False otherwise.</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t67" href="#t67">67</a></span><span class="t"><span class="str">    """</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t68" href="#t68">68</a></span><span class="t">    <span class="nam">print</span><span class="op">(</span><span class="str">f"Starting GeoTIFF generation process using config: {config_path}..."</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t69" href="#t69">69</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t70" href="#t70">70</a></span><span class="t">    <span class="com"># --- Configuration Loading ---</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t71" href="#t71">71</a></span><span class="t">    <span class="key">try</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t72" href="#t72">72</a></span><span class="t">        <span class="nam">config</span> <span class="op">=</span> <span class="nam">toml</span><span class="op">.</span><span class="nam">load</span><span class="op">(</span><span class="nam">config_path</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t73" href="#t73">73</a></span><span class="t">    <span class="key">except</span> <span class="nam">FileNotFoundError</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t74" href="#t74">74</a></span><span class="t">        <span class="nam">print</span><span class="op">(</span><span class="str">f"Fehler: Konfigurationsdatei {config_path} nicht gefunden."</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t75" href="#t75">75</a></span><span class="t">        <span class="key">return</span> <span class="key">False</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t76" href="#t76">76</a></span><span class="t">    <span class="key">except</span> <span class="nam">Exception</span> <span class="key">as</span> <span class="nam">e</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t77" href="#t77">77</a></span><span class="t">        <span class="nam">print</span><span class="op">(</span><span class="str">f"Fehler beim Laden der Konfigurationsdatei {config_path}: {e}"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t78" href="#t78">78</a></span><span class="t">        <span class="key">return</span> <span class="key">False</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t79" href="#t79">79</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t80" href="#t80">80</a></span><span class="t">    <span class="com"># --- Paths from Configuration ---</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t81" href="#t81">81</a></span><span class="t">    <span class="nam">hsi_data_dir</span> <span class="op">=</span> <span class="nam">config</span><span class="op">[</span><span class="str">'paths'</span><span class="op">]</span><span class="op">[</span><span class="str">'hsi_data_directory'</span><span class="op">]</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t82" href="#t82">82</a></span><span class="t">    <span class="nam">hsi_base_filename</span> <span class="op">=</span> <span class="nam">config</span><span class="op">[</span><span class="str">'paths'</span><span class="op">]</span><span class="op">[</span><span class="str">'hsi_base_filename'</span><span class="op">]</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t83" href="#t83">83</a></span><span class="t">    <span class="nam">hdr_file_path</span> <span class="op">=</span> <span class="nam">os</span><span class="op">.</span><span class="nam">path</span><span class="op">.</span><span class="nam">join</span><span class="op">(</span><span class="nam">hsi_data_dir</span><span class="op">,</span> <span class="str">f"{hsi_base_filename}.hdr"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t84" href="#t84">84</a></span><span class="t">    <span class="nam">img_file_path</span> <span class="op">=</span> <span class="nam">os</span><span class="op">.</span><span class="nam">path</span><span class="op">.</span><span class="nam">join</span><span class="op">(</span><span class="nam">hsi_data_dir</span><span class="op">,</span> <span class="str">f"{hsi_base_filename}.img"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t85" href="#t85">85</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t86" href="#t86">86</a></span><span class="t">    <span class="nam">output_dir</span> <span class="op">=</span> <span class="nam">config</span><span class="op">[</span><span class="str">'paths'</span><span class="op">]</span><span class="op">[</span><span class="str">'output_directory'</span><span class="op">]</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t87" href="#t87">87</a></span><span class="t">    <span class="nam">os</span><span class="op">.</span><span class="nam">makedirs</span><span class="op">(</span><span class="nam">output_dir</span><span class="op">,</span> <span class="nam">exist_ok</span><span class="op">=</span><span class="key">True</span><span class="op">)</span> <span class="com"># Ensure output directory exists</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t88" href="#t88">88</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t89" href="#t89">89</a></span><span class="t">    <span class="nam">georeferenced_pixels_input_filename</span> <span class="op">=</span> <span class="nam">config</span><span class="op">[</span><span class="str">'paths'</span><span class="op">]</span><span class="op">[</span><span class="str">'georeferenced_pixels_csv'</span><span class="op">]</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t90" href="#t90">90</a></span><span class="t">    <span class="nam">georeferenced_pixels_input_path</span> <span class="op">=</span> <span class="nam">os</span><span class="op">.</span><span class="nam">path</span><span class="op">.</span><span class="nam">join</span><span class="op">(</span><span class="nam">output_dir</span><span class="op">,</span> <span class="nam">georeferenced_pixels_input_filename</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t91" href="#t91">91</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t92" href="#t92">92</a></span><span class="t">    <span class="nam">base_geotiff_output_filename</span> <span class="op">=</span> <span class="nam">config</span><span class="op">[</span><span class="str">'paths'</span><span class="op">]</span><span class="op">[</span><span class="str">'georeferenced_rgb_tif'</span><span class="op">]</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t93" href="#t93">93</a></span><span class="t">    <span class="nam">timestamp</span> <span class="op">=</span> <span class="nam">datetime</span><span class="op">.</span><span class="nam">datetime</span><span class="op">.</span><span class="nam">now</span><span class="op">(</span><span class="op">)</span><span class="op">.</span><span class="nam">strftime</span><span class="op">(</span><span class="str">"%Y%m%d%H%M%S"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t94" href="#t94">94</a></span><span class="t">    <span class="nam">name</span><span class="op">,</span> <span class="nam">ext</span> <span class="op">=</span> <span class="nam">os</span><span class="op">.</span><span class="nam">path</span><span class="op">.</span><span class="nam">splitext</span><span class="op">(</span><span class="nam">base_geotiff_output_filename</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t95" href="#t95">95</a></span><span class="t">    <span class="nam">geotiff_output_filename_with_ts</span> <span class="op">=</span> <span class="str">f"{name}_{timestamp}{ext}"</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t96" href="#t96">96</a></span><span class="t">    <span class="nam">geotiff_output_path</span> <span class="op">=</span> <span class="nam">os</span><span class="op">.</span><span class="nam">path</span><span class="op">.</span><span class="nam">join</span><span class="op">(</span><span class="nam">output_dir</span><span class="op">,</span> <span class="nam">geotiff_output_filename_with_ts</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t97" href="#t97">97</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t98" href="#t98">98</a></span><span class="t">    <span class="com"># --- Parameters from Configuration ---</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t99" href="#t99">99</a></span><span class="t">    <span class="nam">target_R_nm</span> <span class="op">=</span> <span class="nam">config</span><span class="op">[</span><span class="str">'parameters'</span><span class="op">]</span><span class="op">[</span><span class="str">'rgb_geotiff_creation'</span><span class="op">]</span><span class="op">[</span><span class="str">'target_wavelength_R_nm'</span><span class="op">]</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t100" href="#t100">100</a></span><span class="t">    <span class="nam">target_G_nm</span> <span class="op">=</span> <span class="nam">config</span><span class="op">[</span><span class="str">'parameters'</span><span class="op">]</span><span class="op">[</span><span class="str">'rgb_geotiff_creation'</span><span class="op">]</span><span class="op">[</span><span class="str">'target_wavelength_G_nm'</span><span class="op">]</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t101" href="#t101">101</a></span><span class="t">    <span class="nam">target_B_nm</span> <span class="op">=</span> <span class="nam">config</span><span class="op">[</span><span class="str">'parameters'</span><span class="op">]</span><span class="op">[</span><span class="str">'rgb_geotiff_creation'</span><span class="op">]</span><span class="op">[</span><span class="str">'target_wavelength_B_nm'</span><span class="op">]</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t102" href="#t102">102</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t103" href="#t103">103</a></span><span class="t">    <span class="nam">TARGET_WAVELENGTHS_NM</span> <span class="op">=</span> <span class="op">{</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t104" href="#t104">104</a></span><span class="t">        <span class="str">'R'</span><span class="op">:</span> <span class="nam">target_R_nm</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t105" href="#t105">105</a></span><span class="t">        <span class="str">'G'</span><span class="op">:</span> <span class="nam">target_G_nm</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t106" href="#t106">106</a></span><span class="t">        <span class="str">'B'</span><span class="op">:</span> <span class="nam">target_B_nm</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t107" href="#t107">107</a></span><span class="t">    <span class="op">}</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t108" href="#t108">108</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t109" href="#t109">109</a></span><span class="t">    <span class="nam">TARGET_RESOLUTION_METERS</span> <span class="op">=</span> <span class="nam">config</span><span class="op">[</span><span class="str">'parameters'</span><span class="op">]</span><span class="op">[</span><span class="str">'rgb_geotiff_creation'</span><span class="op">]</span><span class="op">[</span><span class="str">'target_resolution_meters'</span><span class="op">]</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t110" href="#t110">110</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t111" href="#t111">111</a></span><span class="t">    <span class="nam">output_epsg_code_val</span> <span class="op">=</span> <span class="nam">config</span><span class="op">[</span><span class="str">'parameters'</span><span class="op">]</span><span class="op">[</span><span class="str">'rgb_geotiff_creation'</span><span class="op">]</span><span class="op">[</span><span class="str">'output_epsg_code'</span><span class="op">]</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t112" href="#t112">112</a></span><span class="t">    <span class="key">if</span> <span class="nam">isinstance</span><span class="op">(</span><span class="nam">output_epsg_code_val</span><span class="op">,</span> <span class="op">(</span><span class="nam">int</span><span class="op">,</span> <span class="nam">float</span><span class="op">)</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t113" href="#t113">113</a></span><span class="t">        <span class="nam">TARGET_CRS</span> <span class="op">=</span> <span class="str">f"EPSG:{int(output_epsg_code_val)}"</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t114" href="#t114">114</a></span><span class="t">    <span class="key">elif</span> <span class="nam">isinstance</span><span class="op">(</span><span class="nam">output_epsg_code_val</span><span class="op">,</span> <span class="nam">str</span><span class="op">)</span> <span class="key">and</span> <span class="key">not</span> <span class="nam">output_epsg_code_val</span><span class="op">.</span><span class="nam">upper</span><span class="op">(</span><span class="op">)</span><span class="op">.</span><span class="nam">startswith</span><span class="op">(</span><span class="str">"EPSG:"</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t115" href="#t115">115</a></span><span class="t">        <span class="nam">TARGET_CRS</span> <span class="op">=</span> <span class="str">f"EPSG:{output_epsg_code_val}"</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t116" href="#t116">116</a></span><span class="t">    <span class="key">else</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t117" href="#t117">117</a></span><span class="t">        <span class="nam">TARGET_CRS</span> <span class="op">=</span> <span class="nam">str</span><span class="op">(</span><span class="nam">output_epsg_code_val</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t118" href="#t118">118</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t119" href="#t119">119</a></span><span class="t">    <span class="nam">normalization_method</span> <span class="op">=</span> <span class="nam">config</span><span class="op">[</span><span class="str">'parameters'</span><span class="op">]</span><span class="op">[</span><span class="str">'rgb_geotiff_creation'</span><span class="op">]</span><span class="op">[</span><span class="str">'normalization_method'</span><span class="op">]</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t120" href="#t120">120</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t121" href="#t121">121</a></span><span class="t">    <span class="com"># 1. HSI-Daten laden und Band-Auswahl</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t122" href="#t122">122</a></span><span class="t">    <span class="nam">print</span><span class="op">(</span><span class="str">f"Loading HSI data from {hdr_file_path} and {img_file_path}..."</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t123" href="#t123">123</a></span><span class="t">    <span class="key">try</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t124" href="#t124">124</a></span><span class="t">        <span class="nam">img_hsi</span> <span class="op">=</span> <span class="nam">spectral</span><span class="op">.</span><span class="nam">open_image</span><span class="op">(</span><span class="nam">hdr_file_path</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t125" href="#t125">125</a></span><span class="t">        <span class="nam">hsi_data</span> <span class="op">=</span> <span class="nam">img_hsi</span><span class="op">.</span><span class="nam">load</span><span class="op">(</span><span class="op">)</span> <span class="com"># Loads data as (lines, samples, bands)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t126" href="#t126">126</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t127" href="#t127">127</a></span><span class="t">        <span class="nam">hsi_lines</span> <span class="op">=</span> <span class="nam">img_hsi</span><span class="op">.</span><span class="nam">shape</span><span class="op">[</span><span class="num">0</span><span class="op">]</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t128" href="#t128">128</a></span><span class="t">        <span class="nam">hsi_samples</span> <span class="op">=</span> <span class="nam">img_hsi</span><span class="op">.</span><span class="nam">shape</span><span class="op">[</span><span class="num">1</span><span class="op">]</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t129" href="#t129">129</a></span><span class="t">        <span class="nam">hsi_num_bands</span> <span class="op">=</span> <span class="nam">img_hsi</span><span class="op">.</span><span class="nam">shape</span><span class="op">[</span><span class="num">2</span><span class="op">]</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t130" href="#t130">130</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t131" href="#t131">131</a></span><span class="t">        <span class="key">if</span> <span class="nam">hasattr</span><span class="op">(</span><span class="nam">img_hsi</span><span class="op">,</span> <span class="str">'bands'</span><span class="op">)</span> <span class="key">and</span> <span class="nam">hasattr</span><span class="op">(</span><span class="nam">img_hsi</span><span class="op">.</span><span class="nam">bands</span><span class="op">,</span> <span class="str">'centers'</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t132" href="#t132">132</a></span><span class="t">            <span class="nam">available_wavelengths</span> <span class="op">=</span> <span class="op">[</span><span class="nam">float</span><span class="op">(</span><span class="nam">w</span><span class="op">)</span> <span class="key">for</span> <span class="nam">w</span> <span class="key">in</span> <span class="nam">img_hsi</span><span class="op">.</span><span class="nam">bands</span><span class="op">.</span><span class="nam">centers</span><span class="op">]</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t133" href="#t133">133</a></span><span class="t">        <span class="key">elif</span> <span class="str">'wavelength'</span> <span class="key">in</span> <span class="nam">img_hsi</span><span class="op">.</span><span class="nam">metadata</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t134" href="#t134">134</a></span><span class="t">            <span class="nam">available_wavelengths_str</span> <span class="op">=</span> <span class="nam">img_hsi</span><span class="op">.</span><span class="nam">metadata</span><span class="op">[</span><span class="str">'wavelength'</span><span class="op">]</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t135" href="#t135">135</a></span><span class="t">            <span class="nam">available_wavelengths</span> <span class="op">=</span> <span class="op">[</span><span class="nam">float</span><span class="op">(</span><span class="nam">w</span><span class="op">)</span> <span class="key">for</span> <span class="nam">w</span> <span class="key">in</span> <span class="nam">available_wavelengths_str</span><span class="op">]</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t136" href="#t136">136</a></span><span class="t">        <span class="key">else</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t137" href="#t137">137</a></span><span class="t">            <span class="key">raise</span> <span class="nam">ValueError</span><span class="op">(</span><span class="str">"Wavelength information not found in HSI header metadata or bands attribute."</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t138" href="#t138">138</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t139" href="#t139">139</a></span><span class="t">        <span class="nam">print</span><span class="op">(</span><span class="str">f"HSI data dimensions: Lines={hsi_lines}, Samples={hsi_samples}, Bands={hsi_num_bands}"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t140" href="#t140">140</a></span><span class="t">        <span class="key">if</span> <span class="nam">available_wavelengths</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t141" href="#t141">141</a></span><span class="t">            <span class="nam">print</span><span class="op">(</span><span class="str">f"First 10 available wavelengths: {available_wavelengths[:10]} nm"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t142" href="#t142">142</a></span><span class="t">        <span class="key">else</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t143" href="#t143">143</a></span><span class="t">            <span class="nam">print</span><span class="op">(</span><span class="str">"Warning: No wavelengths could be extracted."</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t144" href="#t144">144</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t145" href="#t145">145</a></span><span class="t">    <span class="key">except</span> <span class="nam">FileNotFoundError</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t146" href="#t146">146</a></span><span class="t">        <span class="nam">print</span><span class="op">(</span><span class="str">f"Error: HSI file not found. Searched for .hdr at '{hdr_file_path}' and .img (implicitly by spectral)."</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t147" href="#t147">147</a></span><span class="t">        <span class="nam">print</span><span class="op">(</span><span class="str">"Please ensure the HSI header and image files are correctly specified and accessible."</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t148" href="#t148">148</a></span><span class="t">        <span class="key">return</span> <span class="key">False</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t149" href="#t149">149</a></span><span class="t">    <span class="key">except</span> <span class="nam">Exception</span> <span class="key">as</span> <span class="nam">e</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t150" href="#t150">150</a></span><span class="t">        <span class="nam">print</span><span class="op">(</span><span class="str">f"Error loading HSI data: {e}"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t151" href="#t151">151</a></span><span class="t">        <span class="key">return</span> <span class="key">False</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t152" href="#t152">152</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t153" href="#t153">153</a></span><span class="t">    <span class="nam">band_indices</span> <span class="op">=</span> <span class="op">{</span><span class="op">}</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t154" href="#t154">154</a></span><span class="t">    <span class="key">for</span> <span class="nam">color</span><span class="op">,</span> <span class="nam">target_wl</span> <span class="key">in</span> <span class="nam">TARGET_WAVELENGTHS_NM</span><span class="op">.</span><span class="nam">items</span><span class="op">(</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t155" href="#t155">155</a></span><span class="t">        <span class="nam">band_indices</span><span class="op">[</span><span class="nam">color</span><span class="op">]</span> <span class="op">=</span> <span class="nam">find_nearest_band_index</span><span class="op">(</span><span class="nam">available_wavelengths</span><span class="op">,</span> <span class="nam">target_wl</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t156" href="#t156">156</a></span><span class="t">        <span class="nam">print</span><span class="op">(</span><span class="str">f"Target {color} ({target_wl}nm) -> Closest band index: {band_indices[color]} (Wavelength: {available_wavelengths[band_indices[color]]:.2f}nm)"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t157" href="#t157">157</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t158" href="#t158">158</a></span><span class="t">    <span class="com"># 2. Georeferenzierungsparameter und Zielraster definieren</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t159" href="#t159">159</a></span><span class="t">    <span class="nam">print</span><span class="op">(</span><span class="str">f"Loading georeferencing data from {georeferenced_pixels_input_path}..."</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t160" href="#t160">160</a></span><span class="t">    <span class="key">try</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t161" href="#t161">161</a></span><span class="t">        <span class="nam">geo_df</span> <span class="op">=</span> <span class="nam">pd</span><span class="op">.</span><span class="nam">read_csv</span><span class="op">(</span><span class="nam">georeferenced_pixels_input_path</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t162" href="#t162">162</a></span><span class="t">    <span class="key">except</span> <span class="nam">FileNotFoundError</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t163" href="#t163">163</a></span><span class="t">        <span class="nam">print</span><span class="op">(</span><span class="str">f"Error: Georeferencing CSV file not found at '{georeferenced_pixels_input_path}'."</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t164" href="#t164">164</a></span><span class="t">        <span class="key">return</span> <span class="key">False</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t165" href="#t165">165</a></span><span class="t">    <span class="key">except</span> <span class="nam">Exception</span> <span class="key">as</span> <span class="nam">e</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t166" href="#t166">166</a></span><span class="t">        <span class="nam">print</span><span class="op">(</span><span class="str">f"Error reading CSV {georeferenced_pixels_input_path}: {e}"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t167" href="#t167">167</a></span><span class="t">        <span class="key">return</span> <span class="key">False</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t168" href="#t168">168</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t169" href="#t169">169</a></span><span class="t">    <span class="key">if</span> <span class="nam">geo_df</span><span class="op">.</span><span class="nam">empty</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t170" href="#t170">170</a></span><span class="t">        <span class="nam">print</span><span class="op">(</span><span class="str">f"Error: Georeferencing CSV file '{georeferenced_pixels_input_path}' is empty."</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t171" href="#t171">171</a></span><span class="t">        <span class="key">return</span> <span class="key">False</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t172" href="#t172">172</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t173" href="#t173">173</a></span><span class="t">    <span class="nam">min_x</span><span class="op">,</span> <span class="nam">max_x</span> <span class="op">=</span> <span class="nam">geo_df</span><span class="op">[</span><span class="str">'X_ground'</span><span class="op">]</span><span class="op">.</span><span class="nam">min</span><span class="op">(</span><span class="op">)</span><span class="op">,</span> <span class="nam">geo_df</span><span class="op">[</span><span class="str">'X_ground'</span><span class="op">]</span><span class="op">.</span><span class="nam">max</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t174" href="#t174">174</a></span><span class="t">    <span class="nam">min_y</span><span class="op">,</span> <span class="nam">max_y</span> <span class="op">=</span> <span class="nam">geo_df</span><span class="op">[</span><span class="str">'Y_ground'</span><span class="op">]</span><span class="op">.</span><span class="nam">min</span><span class="op">(</span><span class="op">)</span><span class="op">,</span> <span class="nam">geo_df</span><span class="op">[</span><span class="str">'Y_ground'</span><span class="op">]</span><span class="op">.</span><span class="nam">max</span><span class="op">(</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t175" href="#t175">175</a></span><span class="t">    <span class="nam">print</span><span class="op">(</span><span class="str">f"Bounding Box (X_ground, Y_ground): Min=({min_x:.2f}, {min_y:.2f}), Max=({max_x:.2f}, {max_y:.2f})"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t176" href="#t176">176</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t177" href="#t177">177</a></span><span class="t">    <span class="nam">target_width</span> <span class="op">=</span> <span class="nam">int</span><span class="op">(</span><span class="nam">np</span><span class="op">.</span><span class="nam">ceil</span><span class="op">(</span><span class="op">(</span><span class="nam">max_x</span> <span class="op">-</span> <span class="nam">min_x</span><span class="op">)</span> <span class="op">/</span> <span class="nam">TARGET_RESOLUTION_METERS</span><span class="op">)</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t178" href="#t178">178</a></span><span class="t">    <span class="nam">target_height</span> <span class="op">=</span> <span class="nam">int</span><span class="op">(</span><span class="nam">np</span><span class="op">.</span><span class="nam">ceil</span><span class="op">(</span><span class="op">(</span><span class="nam">max_y</span> <span class="op">-</span> <span class="nam">min_y</span><span class="op">)</span> <span class="op">/</span> <span class="nam">TARGET_RESOLUTION_METERS</span><span class="op">)</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t179" href="#t179">179</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t180" href="#t180">180</a></span><span class="t">    <span class="key">if</span> <span class="nam">target_width</span> <span class="op">&lt;=</span> <span class="num">0</span> <span class="key">or</span> <span class="nam">target_height</span> <span class="op">&lt;=</span> <span class="num">0</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t181" href="#t181">181</a></span><span class="t">        <span class="nam">print</span><span class="op">(</span><span class="str">f"Error: Invalid target dimensions calculated. Width={target_width}, Height={target_height}."</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t182" href="#t182">182</a></span><span class="t">        <span class="nam">print</span><span class="op">(</span><span class="str">"This might be due to issues in georeferenced_pixels.csv (e.g., all points are identical or min/max are swapped)."</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t183" href="#t183">183</a></span><span class="t">        <span class="key">return</span> <span class="key">False</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t184" href="#t184">184</a></span><span class="t">    <span class="nam">print</span><span class="op">(</span><span class="str">f"Target GeoTIFF dimensions: Width={target_width}px, Height={target_height}px"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t185" href="#t185">185</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t186" href="#t186">186</a></span><span class="t">    <span class="nam">transform</span> <span class="op">=</span> <span class="nam">Affine</span><span class="op">(</span><span class="nam">TARGET_RESOLUTION_METERS</span><span class="op">,</span> <span class="num">0.0</span><span class="op">,</span> <span class="nam">min_x</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t187" href="#t187">187</a></span><span class="t">                       <span class="num">0.0</span><span class="op">,</span> <span class="op">-</span><span class="nam">TARGET_RESOLUTION_METERS</span><span class="op">,</span> <span class="nam">max_y</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t188" href="#t188">188</a></span><span class="t">    <span class="nam">print</span><span class="op">(</span><span class="str">f"GeoTransform: {transform}"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t189" href="#t189">189</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t190" href="#t190">190</a></span><span class="t">    <span class="com"># 3. Resampling der HSI-Daten auf das Zielraster (Nearest Neighbour)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t191" href="#t191">191</a></span><span class="t">    <span class="nam">print</span><span class="op">(</span><span class="str">"Resampling HSI data to target raster..."</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t192" href="#t192">192</a></span><span class="t">    <span class="nam">target_rgb_image</span> <span class="op">=</span> <span class="nam">np</span><span class="op">.</span><span class="nam">full</span><span class="op">(</span><span class="op">(</span><span class="nam">target_height</span><span class="op">,</span> <span class="nam">target_width</span><span class="op">,</span> <span class="num">3</span><span class="op">)</span><span class="op">,</span> <span class="nam">NO_DATA_VALUE</span><span class="op">,</span> <span class="nam">dtype</span><span class="op">=</span><span class="nam">np</span><span class="op">.</span><span class="nam">float32</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t193" href="#t193">193</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t194" href="#t194">194</a></span><span class="t">    <span class="nam">source_coords</span> <span class="op">=</span> <span class="nam">geo_df</span><span class="op">[</span><span class="op">[</span><span class="str">'X_ground'</span><span class="op">,</span> <span class="str">'Y_ground'</span><span class="op">]</span><span class="op">]</span><span class="op">.</span><span class="nam">values</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t195" href="#t195">195</a></span><span class="t">    <span class="key">try</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t196" href="#t196">196</a></span><span class="t">        <span class="nam">kdtree</span> <span class="op">=</span> <span class="nam">KDTree</span><span class="op">(</span><span class="nam">source_coords</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t197" href="#t197">197</a></span><span class="t">    <span class="key">except</span> <span class="nam">Exception</span> <span class="key">as</span> <span class="nam">e</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t198" href="#t198">198</a></span><span class="t">        <span class="nam">print</span><span class="op">(</span><span class="str">f"Error creating KDTree (likely from empty or invalid source_coords): {e}"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t199" href="#t199">199</a></span><span class="t">        <span class="key">if</span> <span class="nam">source_coords</span><span class="op">.</span><span class="nam">shape</span><span class="op">[</span><span class="num">0</span><span class="op">]</span> <span class="op">==</span> <span class="num">0</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t200" href="#t200">200</a></span><span class="t">            <span class="nam">print</span><span class="op">(</span><span class="str">"The georeferenced_pixels.csv might be empty or failed to load properly."</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t201" href="#t201">201</a></span><span class="t">        <span class="key">return</span> <span class="key">False</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t202" href="#t202">202</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t203" href="#t203">203</a></span><span class="t">    <span class="key">for</span> <span class="nam">r</span> <span class="key">in</span> <span class="nam">range</span><span class="op">(</span><span class="nam">target_height</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t204" href="#t204">204</a></span><span class="t">        <span class="key">if</span> <span class="nam">r</span> <span class="op">%</span> <span class="op">(</span><span class="nam">max</span><span class="op">(</span><span class="num">1</span><span class="op">,</span> <span class="nam">target_height</span> <span class="op">//</span> <span class="num">10</span><span class="op">)</span><span class="op">)</span> <span class="op">==</span> <span class="num">0</span> <span class="op">:</span> <span class="com"># Print progress, ensure divisor is at least 1</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t205" href="#t205">205</a></span><span class="t">             <span class="nam">print</span><span class="op">(</span><span class="str">f"Resampling progress: {r/target_height*100:.1f}%"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t206" href="#t206">206</a></span><span class="t">        <span class="key">for</span> <span class="nam">c</span> <span class="key">in</span> <span class="nam">range</span><span class="op">(</span><span class="nam">target_width</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t207" href="#t207">207</a></span><span class="t">            <span class="nam">target_x</span> <span class="op">=</span> <span class="nam">min_x</span> <span class="op">+</span> <span class="op">(</span><span class="nam">c</span> <span class="op">+</span> <span class="num">0.5</span><span class="op">)</span> <span class="op">*</span> <span class="nam">TARGET_RESOLUTION_METERS</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t208" href="#t208">208</a></span><span class="t">            <span class="nam">target_y</span> <span class="op">=</span> <span class="nam">max_y</span> <span class="op">-</span> <span class="op">(</span><span class="nam">r</span> <span class="op">+</span> <span class="num">0.5</span><span class="op">)</span> <span class="op">*</span> <span class="nam">TARGET_RESOLUTION_METERS</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t209" href="#t209">209</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t210" href="#t210">210</a></span><span class="t">            <span class="nam">distance</span><span class="op">,</span> <span class="nam">nearest_idx</span> <span class="op">=</span> <span class="nam">kdtree</span><span class="op">.</span><span class="nam">query</span><span class="op">(</span><span class="op">[</span><span class="nam">target_x</span><span class="op">,</span> <span class="nam">target_y</span><span class="op">]</span><span class="op">,</span> <span class="nam">k</span><span class="op">=</span><span class="num">1</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t211" href="#t211">211</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t212" href="#t212">212</a></span><span class="t">            <span class="key">if</span> <span class="nam">nearest_idx</span> <span class="op">&lt;</span> <span class="num">0</span> <span class="key">or</span> <span class="nam">nearest_idx</span> <span class="op">>=</span> <span class="nam">len</span><span class="op">(</span><span class="nam">geo_df</span><span class="op">)</span><span class="op">:</span> <span class="com"># Check if index is valid</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t213" href="#t213">213</a></span><span class="t">                <span class="com"># This case should ideally not happen with KDTree query if source_coords is not empty</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t214" href="#t214">214</a></span><span class="t">                <span class="nam">print</span><span class="op">(</span><span class="str">f"Warning: Invalid nearest_idx {nearest_idx} from KDTree query. Skipping pixel ({r},{c})."</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t215" href="#t215">215</a></span><span class="t">                <span class="key">continue</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t216" href="#t216">216</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t217" href="#t217">217</a></span><span class="t">            <span class="nam">hsi_line</span> <span class="op">=</span> <span class="nam">geo_df</span><span class="op">.</span><span class="nam">loc</span><span class="op">[</span><span class="nam">nearest_idx</span><span class="op">,</span> <span class="str">'hsi_line_index'</span><span class="op">]</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t218" href="#t218">218</a></span><span class="t">            <span class="nam">hsi_pixel</span> <span class="op">=</span> <span class="nam">geo_df</span><span class="op">.</span><span class="nam">loc</span><span class="op">[</span><span class="nam">nearest_idx</span><span class="op">,</span> <span class="str">'pixel_index'</span><span class="op">]</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t219" href="#t219">219</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t220" href="#t220">220</a></span><span class="t">            <span class="key">if</span> <span class="num">0</span> <span class="op">&lt;=</span> <span class="nam">hsi_line</span> <span class="op">&lt;</span> <span class="nam">hsi_data</span><span class="op">.</span><span class="nam">shape</span><span class="op">[</span><span class="num">0</span><span class="op">]</span> <span class="key">and</span> <span class="num">0</span> <span class="op">&lt;=</span> <span class="nam">hsi_pixel</span> <span class="op">&lt;</span> <span class="nam">hsi_data</span><span class="op">.</span><span class="nam">shape</span><span class="op">[</span><span class="num">1</span><span class="op">]</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t221" href="#t221">221</a></span><span class="t">                <span class="nam">r_val</span> <span class="op">=</span> <span class="nam">hsi_data</span><span class="op">[</span><span class="nam">hsi_line</span><span class="op">,</span> <span class="nam">hsi_pixel</span><span class="op">,</span> <span class="nam">band_indices</span><span class="op">[</span><span class="str">'R'</span><span class="op">]</span><span class="op">]</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t222" href="#t222">222</a></span><span class="t">                <span class="nam">g_val</span> <span class="op">=</span> <span class="nam">hsi_data</span><span class="op">[</span><span class="nam">hsi_line</span><span class="op">,</span> <span class="nam">hsi_pixel</span><span class="op">,</span> <span class="nam">band_indices</span><span class="op">[</span><span class="str">'G'</span><span class="op">]</span><span class="op">]</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t223" href="#t223">223</a></span><span class="t">                <span class="nam">b_val</span> <span class="op">=</span> <span class="nam">hsi_data</span><span class="op">[</span><span class="nam">hsi_line</span><span class="op">,</span> <span class="nam">hsi_pixel</span><span class="op">,</span> <span class="nam">band_indices</span><span class="op">[</span><span class="str">'B'</span><span class="op">]</span><span class="op">]</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t224" href="#t224">224</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t225" href="#t225">225</a></span><span class="t">                <span class="nam">target_rgb_image</span><span class="op">[</span><span class="nam">r</span><span class="op">,</span> <span class="nam">c</span><span class="op">,</span> <span class="num">0</span><span class="op">]</span> <span class="op">=</span> <span class="nam">r_val</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t226" href="#t226">226</a></span><span class="t">                <span class="nam">target_rgb_image</span><span class="op">[</span><span class="nam">r</span><span class="op">,</span> <span class="nam">c</span><span class="op">,</span> <span class="num">1</span><span class="op">]</span> <span class="op">=</span> <span class="nam">g_val</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t227" href="#t227">227</a></span><span class="t">                <span class="nam">target_rgb_image</span><span class="op">[</span><span class="nam">r</span><span class="op">,</span> <span class="nam">c</span><span class="op">,</span> <span class="num">2</span><span class="op">]</span> <span class="op">=</span> <span class="nam">b_val</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t228" href="#t228">228</a></span><span class="t">    <span class="nam">print</span><span class="op">(</span><span class="str">"Resampling complete."</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t229" href="#t229">229</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t230" href="#t230">230</a></span><span class="t">    <span class="com"># 4. Normalisierung der RGB-B&#228;nder</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t231" href="#t231">231</a></span><span class="t">    <span class="nam">print</span><span class="op">(</span><span class="str">f"Normalizing RGB bands using method: {normalization_method}..."</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t232" href="#t232">232</a></span><span class="t">    <span class="nam">normalized_rgb_image</span> <span class="op">=</span> <span class="nam">np</span><span class="op">.</span><span class="nam">zeros_like</span><span class="op">(</span><span class="nam">target_rgb_image</span><span class="op">,</span> <span class="nam">dtype</span><span class="op">=</span><span class="nam">np</span><span class="op">.</span><span class="nam">uint8</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t233" href="#t233">233</a></span><span class="t">    <span class="key">for</span> <span class="nam">i</span> <span class="key">in</span> <span class="nam">range</span><span class="op">(</span><span class="num">3</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t234" href="#t234">234</a></span><span class="t">        <span class="nam">band_data</span> <span class="op">=</span> <span class="nam">target_rgb_image</span><span class="op">[</span><span class="op">:</span><span class="op">,</span> <span class="op">:</span><span class="op">,</span> <span class="nam">i</span><span class="op">]</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t235" href="#t235">235</a></span><span class="t">        <span class="key">if</span> <span class="nam">np</span><span class="op">.</span><span class="nam">all</span><span class="op">(</span> <span class="op">(</span><span class="nam">band_data</span> <span class="op">==</span> <span class="nam">NO_DATA_VALUE</span><span class="op">)</span> <span class="op">|</span> <span class="nam">np</span><span class="op">.</span><span class="nam">isnan</span><span class="op">(</span><span class="nam">band_data</span><span class="op">)</span> <span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t236" href="#t236">236</a></span><span class="t">            <span class="nam">print</span><span class="op">(</span><span class="str">f"Warning: Band {i} contains only NoData or NaN values. It will be all {NO_DATA_VALUE} in the output."</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t237" href="#t237">237</a></span><span class="t">            <span class="nam">normalized_rgb_image</span><span class="op">[</span><span class="op">:</span><span class="op">,</span> <span class="op">:</span><span class="op">,</span> <span class="nam">i</span><span class="op">]</span> <span class="op">=</span> <span class="nam">NO_DATA_VALUE</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t238" href="#t238">238</a></span><span class="t">        <span class="key">else</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t239" href="#t239">239</a></span><span class="t">            <span class="key">try</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t240" href="#t240">240</a></span><span class="t">                <span class="nam">normalized_rgb_image</span><span class="op">[</span><span class="op">:</span><span class="op">,</span> <span class="op">:</span><span class="op">,</span> <span class="nam">i</span><span class="op">]</span> <span class="op">=</span> <span class="nam">normalize_band</span><span class="op">(</span><span class="nam">band_data</span><span class="op">,</span> <span class="nam">method</span><span class="op">=</span><span class="nam">normalization_method</span><span class="op">,</span> <span class="nam">no_data_val</span><span class="op">=</span><span class="nam">NO_DATA_VALUE</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t241" href="#t241">241</a></span><span class="t">            <span class="key">except</span> <span class="nam">ValueError</span> <span class="key">as</span> <span class="nam">e</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t242" href="#t242">242</a></span><span class="t">                <span class="nam">print</span><span class="op">(</span><span class="str">f"Error during normalization of band {i}: {e}"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t243" href="#t243">243</a></span><span class="t">                <span class="key">return</span> <span class="key">False</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t244" href="#t244">244</a></span><span class="t">    <span class="nam">print</span><span class="op">(</span><span class="str">"Normalization complete."</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t245" href="#t245">245</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t246" href="#t246">246</a></span><span class="t">    <span class="com"># 5. GeoTIFF speichern</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t247" href="#t247">247</a></span><span class="t">    <span class="nam">print</span><span class="op">(</span><span class="str">f"Saving GeoTIFF to {geotiff_output_path}..."</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t248" href="#t248">248</a></span><span class="t">    <span class="key">try</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t249" href="#t249">249</a></span><span class="t">        <span class="key">with</span> <span class="nam">rasterio</span><span class="op">.</span><span class="nam">open</span><span class="op">(</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t250" href="#t250">250</a></span><span class="t">            <span class="nam">geotiff_output_path</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t251" href="#t251">251</a></span><span class="t">            <span class="str">'w'</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t252" href="#t252">252</a></span><span class="t">            <span class="nam">driver</span><span class="op">=</span><span class="str">'GTiff'</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t253" href="#t253">253</a></span><span class="t">            <span class="nam">height</span><span class="op">=</span><span class="nam">target_height</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t254" href="#t254">254</a></span><span class="t">            <span class="nam">width</span><span class="op">=</span><span class="nam">target_width</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t255" href="#t255">255</a></span><span class="t">            <span class="nam">count</span><span class="op">=</span><span class="num">3</span><span class="op">,</span> <span class="com"># R, G, B</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t256" href="#t256">256</a></span><span class="t">            <span class="nam">dtype</span><span class="op">=</span><span class="nam">rasterio</span><span class="op">.</span><span class="nam">uint8</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t257" href="#t257">257</a></span><span class="t">            <span class="nam">crs</span><span class="op">=</span><span class="nam">TARGET_CRS</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t258" href="#t258">258</a></span><span class="t">            <span class="nam">transform</span><span class="op">=</span><span class="nam">transform</span><span class="op">,</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t259" href="#t259">259</a></span><span class="t">            <span class="nam">nodata</span><span class="op">=</span><span class="nam">NO_DATA_VALUE</span> <span class="key">if</span> <span class="nam">isinstance</span><span class="op">(</span><span class="nam">NO_DATA_VALUE</span><span class="op">,</span> <span class="op">(</span><span class="nam">int</span><span class="op">,</span> <span class="nam">float</span><span class="op">)</span><span class="op">)</span> <span class="key">else</span> <span class="key">None</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t260" href="#t260">260</a></span><span class="t">        <span class="op">)</span> <span class="key">as</span> <span class="nam">dst</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t261" href="#t261">261</a></span><span class="t">            <span class="nam">dst</span><span class="op">.</span><span class="nam">write</span><span class="op">(</span><span class="nam">normalized_rgb_image</span><span class="op">.</span><span class="nam">transpose</span><span class="op">(</span><span class="num">2</span><span class="op">,</span> <span class="num">0</span><span class="op">,</span> <span class="num">1</span><span class="op">)</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t262" href="#t262">262</a></span><span class="t">        <span class="nam">print</span><span class="op">(</span><span class="str">f"GeoTIFF successfully saved to {geotiff_output_path}"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t263" href="#t263">263</a></span><span class="t">    <span class="key">except</span> <span class="nam">Exception</span> <span class="key">as</span> <span class="nam">e</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t264" href="#t264">264</a></span><span class="t">        <span class="nam">print</span><span class="op">(</span><span class="str">f"Error saving GeoTIFF: {e}"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t265" href="#t265">265</a></span><span class="t">        <span class="key">return</span> <span class="key">False</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t266" href="#t266">266</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t267" href="#t267">267</a></span><span class="t">    <span class="nam">print</span><span class="op">(</span><span class="str">"Processing finished successfully."</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="mis show_mis"><span class="n"><a id="t268" href="#t268">268</a></span><span class="t">    <span class="key">return</span> <span class="key">True</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t269" href="#t269">269</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="exc show_exc"><span class="n"><a id="t270" href="#t270">270</a></span><span class="t"><span class="key">if</span> <span class="nam">__name__</span> <span class="op">==</span> <span class="str">"__main__"</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="exc show_exc"><span class="n"><a id="t271" href="#t271">271</a></span><span class="t">    <span class="nam">DEFAULT_CONFIG_PATH</span> <span class="op">=</span> <span class="str">'config.toml'</span>&nbsp;</span><span class="r"></span></p>
    <p class="exc show_exc"><span class="n"><a id="t272" href="#t272">272</a></span><span class="t">    <span class="nam">print</span><span class="op">(</span><span class="str">f"Erstelle georeferenziertes RGB GeoTIFF mit Konfiguration: {DEFAULT_CONFIG_PATH}"</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t273" href="#t273">273</a></span><span class="t">&nbsp;</span><span class="r"></span></p>
    <p class="pln"><span class="n"><a id="t274" href="#t274">274</a></span><span class="t">    <span class="com"># Ensure the default config file exists before trying to run</span>&nbsp;</span><span class="r"></span></p>
    <p class="exc show_exc"><span class="n"><a id="t275" href="#t275">275</a></span><span class="t">    <span class="key">if</span> <span class="key">not</span> <span class="nam">os</span><span class="op">.</span><span class="nam">path</span><span class="op">.</span><span class="nam">exists</span><span class="op">(</span><span class="nam">DEFAULT_CONFIG_PATH</span><span class="op">)</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="exc show_exc"><span class="n"><a id="t276" href="#t276">276</a></span><span class="t">        <span class="nam">print</span><span class="op">(</span><span class="str">f"Fehler: Die Standard-Konfigurationsdatei '{DEFAULT_CONFIG_PATH}' wurde nicht gefunden."</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="exc show_exc"><span class="n"><a id="t277" href="#t277">277</a></span><span class="t">        <span class="nam">print</span><span class="op">(</span><span class="str">"Bitte stellen Sie sicher, dass die Datei existiert oder geben Sie einen g&#252;ltigen Pfad an, wenn Sie das Skript als Modul verwenden."</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="exc show_exc"><span class="n"><a id="t278" href="#t278">278</a></span><span class="t">    <span class="key">else</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="exc show_exc"><span class="n"><a id="t279" href="#t279">279</a></span><span class="t">        <span class="nam">success</span> <span class="op">=</span> <span class="nam">run_create_rgb_geotiff</span><span class="op">(</span><span class="nam">config_path</span><span class="op">=</span><span class="nam">DEFAULT_CONFIG_PATH</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="exc show_exc"><span class="n"><a id="t280" href="#t280">280</a></span><span class="t">        <span class="key">if</span> <span class="nam">success</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="exc show_exc"><span class="n"><a id="t281" href="#t281">281</a></span><span class="t">            <span class="nam">print</span><span class="op">(</span><span class="str">"Georeferenziertes RGB GeoTIFF erfolgreich erstellt."</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
    <p class="exc show_exc"><span class="n"><a id="t282" href="#t282">282</a></span><span class="t">        <span class="key">else</span><span class="op">:</span>&nbsp;</span><span class="r"></span></p>
    <p class="exc show_exc"><span class="n"><a id="t283" href="#t283">283</a></span><span class="t">            <span class="nam">print</span><span class="op">(</span><span class="str">"Fehler bei der Erstellung des georeferenzierten RGB GeoTIFFs."</span><span class="op">)</span>&nbsp;</span><span class="r"></span></p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="create_consolidated_webodm_poses_py.html">&#xab; prev</a> &nbsp; &nbsp;
            <a class="nav" href="index.html">&Hat; index</a> &nbsp; &nbsp;
            <a class="nav" href="georeference_hsi_pixels_py.html">&#xbb; next</a>
            &nbsp; &nbsp; &nbsp;
            <a class="nav" href="https://coverage.readthedocs.io/en/7.8.2">coverage.py v7.8.2</a>,
            created at 2025-06-02 16:11 +0200
        </p>
    </div>
</footer>
</body>
</html>
