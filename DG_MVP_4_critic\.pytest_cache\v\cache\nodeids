["test_consolidation.py::TestConsolidationLS2Updates::test_function_signature_accepts_config_dict", "test_consolidation.py::TestConsolidationLS2Updates::test_invalid_json_raises_webodm_data_error", "test_consolidation.py::TestConsolidationLS2Updates::test_logging_integration", "test_consolidation.py::TestConsolidationLS2Updates::test_missing_config_key_raises_pipeline_config_error", "test_consolidation.py::TestConsolidationLS2Updates::test_missing_features_key_raises_webodm_data_error", "test_consolidation.py::TestConsolidationLS2Updates::test_missing_shots_file_raises_input_data_error", "test_consolidation.py::TestConsolidationLS2Updates::test_successful_consolidation_with_valid_data", "test_georeferencing.py::TestDSMIntersection::test_ray_encounters_nodata_at_sensor_xy", "test_georeferencing.py::TestDSMIntersection::test_ray_exits_dsm_bounds_xy", "test_georeferencing.py::TestDSMIntersection::test_ray_marching_max_distance_exceeded", "test_georeferencing.py::TestDSMIntersection::test_ray_marching_with_sloped_surface", "test_georeferencing.py::TestDSMIntersection::test_ray_misses_dsm_points_away", "test_georeferencing.py::TestDSMIntersection::test_ray_starts_below_points_up", "test_georeferencing.py::TestDSMIntersection::test_successful_intersection_from_above", "test_georeferencing.py::TestGeoreferencingLS2Updates::test_function_signature_accepts_config_dict", "test_georeferencing.py::TestGeoreferencingLS2Updates::test_missing_config_key_raises_pipeline_config_error", "test_georeferencing.py::TestHelperFunctions::test_parse_hsi_header_missing_file", "test_georeferencing.py::TestHelperFunctions::test_parse_hsi_header_missing_lines", "test_georeferencing.py::TestHelperFunctions::test_parse_hsi_header_missing_samples", "test_georeferencing.py::TestHelperFunctions::test_parse_hsi_header_no_lever_arm", "test_georeferencing.py::TestHelperFunctions::test_parse_hsi_header_success", "test_georeferencing.py::TestHelperFunctions::test_parse_sensor_model_missing_file", "test_georeferencing.py::TestHelperFunctions::test_parse_sensor_model_success", "test_georeferencing.py::TestHelperFunctions::test_parse_sensor_model_too_few_entries", "test_georeferencing.py::TestHelperFunctions::test_parse_sensor_model_too_many_entries", "test_georeferencing.py::TestLoggingIntegration::test_logging_integration", "test_georeferencing.py::TestSensorModelParsing::test_parse_sensor_model_angles_in_degrees", "test_georeferencing.py::TestSensorModelParsing::test_parse_sensor_model_angles_in_radians", "test_georeferencing.py::TestVectorizationIntegration::test_vectorized_processing_called_for_flat_plane", "test_georeferencing.py::TestVectorizedExceptionHandling::test_invalid_quaternion_handling_in_vectorized_function", "test_georeferencing.py::TestVectorizedExceptionHandling::test_run_georeferencing_fallback_on_pose_transformation_error", "test_georeferencing.py::TestVectorizedExceptionHandling::test_vectorized_processing_specific_exception_fallback", "test_lever_arm.py::TestIntegrationLeverArm::test_lever_arm_integration_scenario", "test_lever_arm.py::TestIntegrationLeverArm::test_lever_arm_precision", "test_lever_arm.py::TestLeverArmDetermination::test_dead_code_coverage_line_75", "test_lever_arm.py::TestLeverArmDetermination::test_lever_arm_both_zero", "test_lever_arm.py::TestLeverArmDetermination::test_lever_arm_config_override", "test_lever_arm.py::TestLeverArmDetermination::test_lever_arm_hdr_only", "test_lever_arm.py::TestLeverArmDetermination::test_lever_arm_hdr_with_zero_config", "test_lever_arm.py::TestLeverArmDetermination::test_lever_arm_none_inputs", "test_lever_arm.py::TestLeverArmDetermination::test_warning_zero_effective_arm_with_nonzero_hdr", "test_lever_arm.py::TestLeverArmValidation::test_invalid_lever_arm_shape", "test_lever_arm.py::TestLeverArmValidation::test_invalid_lever_arm_values", "test_lever_arm.py::TestLeverArmValidation::test_large_lever_arm_warning", "test_lever_arm.py::TestLeverArmValidation::test_none_lever_arm", "test_lever_arm.py::TestLeverArmValidation::test_valid_lever_arm", "test_main_pipeline.py::TestAdditionalErrorCoverage::test_consolidation_exception_coverage", "test_main_pipeline.py::TestAdditionalErrorCoverage::test_consolidation_failure_coverage", "test_main_pipeline.py::TestAdditionalErrorCoverage::test_georeferencing_exception_coverage", "test_main_pipeline.py::TestAdditionalErrorCoverage::test_georeferencing_failure_coverage", "test_main_pipeline.py::TestAdditionalErrorCoverage::test_plotting_exception_coverage", "test_main_pipeline.py::TestAdditionalErrorCoverage::test_rgb_creation_exception_coverage", "test_main_pipeline.py::TestAdditionalErrorCoverage::test_unexpected_exception_coverage", "test_main_pipeline.py::TestConfigurationLoading::test_load_config_with_unicode", "test_main_pipeline.py::TestConfigurationLoading::test_load_invalid_toml_syntax", "test_main_pipeline.py::TestConfigurationLoading::test_load_nonexistent_config", "test_main_pipeline.py::TestConfigurationLoading::test_load_valid_config", "test_main_pipeline.py::TestConfigurationPassing::test_config_object_immutability", "test_main_pipeline.py::TestConfigurationPassing::test_config_passed_to_submodules", "test_main_pipeline.py::TestLoggingIntegration::test_logging_messages", "test_main_pipeline.py::TestLoggingIntegration::test_logging_setup", "test_main_pipeline.py::TestPipelineOrchestration::test_pipeline_config_error", "test_main_pipeline.py::TestPipelineOrchestration::test_pipeline_failure_in_critical_step", "test_main_pipeline.py::TestPipelineOrchestration::test_pipeline_unexpected_error", "test_main_pipeline.py::TestPipelineOrchestration::test_pipeline_warning_in_optional_step", "test_main_pipeline.py::TestPipelineOrchestration::test_successful_pipeline_execution", "test_synchronization.py::TestHelperFunctions::test_convert_hsi_timestamp_to_ns", "test_synchronization.py::TestHelperFunctions::test_interpolate_pose_exact_match", "test_synchronization.py::TestHelperFunctions::test_interpolate_pose_extrapolation_after", "test_synchronization.py::TestHelperFunctions::test_interpolate_pose_extrapolation_before", "test_synchronization.py::TestHelperFunctions::test_interpolate_pose_interpolation", "test_synchronization.py::TestHelperFunctions::test_load_hsi_data_invalid_format", "test_synchronization.py::TestHelperFunctions::test_load_hsi_data_success", "test_synchronization.py::TestHelperFunctions::test_load_webodm_data_missing_columns", "test_synchronization.py::TestHelperFunctions::test_load_webodm_data_success", "test_synchronization.py::TestHelperFunctions::test_parse_hdr_file_missing_file", "test_synchronization.py::TestHelperFunctions::test_parse_hdr_file_success", "test_synchronization.py::TestLoggingIntegration::test_logging_integration", "test_synchronization.py::TestSynchronizationLS2Updates::test_function_signature_accepts_config_dict", "test_synchronization.py::TestSynchronizationLS2Updates::test_missing_config_key_raises_pipeline_config_error", "test_vectorized_georef.py::TestPerformanceBenchmark::test_log_vectorized_vs_iterative_performance", "test_vectorized_georef.py::TestPerformanceBenchmark::test_vectorized_vs_iterative_performance", "test_vectorized_georef.py::TestVectorizedFlatPlaneIntersection::test_flat_plane_intersection_backward_rays", "test_vectorized_georef.py::TestVectorizedFlatPlaneIntersection::test_flat_plane_intersection_no_intersection", "test_vectorized_georef.py::TestVectorizedFlatPlaneIntersection::test_flat_plane_intersection_simple", "test_vectorized_georef.py::TestVectorizedFlatPlaneIntersection::test_flat_plane_intersection_threshold", "test_vectorized_georef.py::TestVectorizedLineProcessing::test_process_hsi_line_flat_plane", "test_vectorized_georef.py::TestVectorizedLineProcessing::test_process_hsi_line_invalid_quaternion", "test_vectorized_georef.py::TestVectorizedSensorViewVectors::test_sensor_view_vectors_multiple_pixels", "test_vectorized_georef.py::TestVectorizedSensorViewVectors::test_sensor_view_vectors_single_pixel", "test_vectorized_georef.py::TestVectorizedSensorViewVectors::test_sensor_view_vectors_with_correction", "test_vectorized_georef.py::TestVectorizedSensorViewVectors::test_sensor_view_vectors_zero_norm_handling", "test_vectorized_georef.py::TestVectorizedWorldTransform::test_world_transform_identity", "test_vectorized_georef.py::TestVectorizedWorldTransform::test_world_transform_multiple_vectors", "test_vectorized_georef.py::TestVectorizedWorldTransform::test_world_transform_rotation"]